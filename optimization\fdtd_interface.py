"""
Minimal FDTD Interface for PSO Optimization
"""

import sys
import time
import numpy as np
import os
from pathlib import Path
from typing import Dict, Optional

# Add Lumerical API path
LUMERICAL_API_PATH = "D:\\Program Files\\Lumerical\\v231\\api\\python"
if LUMERICAL_API_PATH not in sys.path:
    sys.path.append(LUMERICAL_API_PATH)

# Import FDTD simulation components
try:
    import lumapi
    from optimization.pcell_fdtd_wrapper import run_pcell_fdtd_with_dict
    FDTD_AVAILABLE = True
except ImportError as e:
    print(f"Warning: FDTD simulation modules not available: {e}")
    FDTD_AVAILABLE = False


class FDTDInterface:
    """Minimal FDTD simulation interface for PSO optimization."""

    def __init__(self, use_real_fdtd: bool = False):
        """Initialize FDTD interface."""
        self.use_real_fdtd = use_real_fdtd and FDTD_AVAILABLE
        self.simulation_count = 0
        self.session = None

        # Setup FDTD directory for saving simulation files
        self.fdtd_dir = Path("optimization/FDTD")
        self.fdtd_dir.mkdir(parents=True, exist_ok=True)

        # Initialize Lumerical session if using real FDTD
        if self.use_real_fdtd:
            try:
                self.session = lumapi.FDTD(hide=True)
                print("✓ FDTD session initialized")
            except Exception as e:
                print(f"Failed to initialize FDTD session: {e}")
                self.use_real_fdtd = False

        print(f"✓ FDTD Interface: {'REAL' if self.use_real_fdtd else 'MOCK'} simulation")
        print(f"✓ FDTD files will be saved to: {self.fdtd_dir}")

    def simulate(self, params: Dict[str, float]) -> Dict:
        """Run FDTD simulation and return S-parameters."""
        self.simulation_count += 1

        if self.use_real_fdtd and self.session:
            return self._run_real_simulation(params)
        else:
            return self._run_mock_simulation(params)

    def _run_real_simulation(self, params: Dict[str, float]) -> Dict:
        """Run actual FDTD simulation."""
        try:
            # Use the working wrapper with persistent session
            sp = run_pcell_fdtd_with_dict(params, self.session)
            return sp
        except Exception as e:
            print(f"FDTD simulation failed: {e}")
            return self._run_mock_simulation(params)

    def _run_mock_simulation(self, params: Dict[str, float]) -> Dict:
        """Mock simulation for testing."""
        # Simple mock that mimics the real S-parameter format
        # Convert to standard Python float to handle np.float64 inputs
        offset = float(params.get('offset', 40.0))
        radius = float(params.get('radius', 20.0))

        # Mock optimization landscape with minimum around offset=35, radius=20
        offset_dev = (offset - 35.0) / 10.0
        radius_dev = (radius - 20.0) / 5.0
        loss_db = 0.2 + 0.5 * (offset_dev**2 + radius_dev**2) + 0.05 * np.random.random()

        # Mock S-parameters in the same format as real simulation
        transmission = 10**(-loss_db / 10)
        s11_mag = 0.05 + 0.02 * np.random.random()
        s21_mag = np.sqrt(transmission)

        return {
            'f': np.array([[1.93414489e+14]]),
            'S11': np.array([s11_mag * np.exp(1j * 0.1)]),
            'S12': np.array([s21_mag * np.exp(1j * 0.05)]),
            'S21': np.array([s21_mag * np.exp(1j * 0.05)]),
            'S22': np.array([s11_mag * np.exp(1j * 0.1)]),
            'Lumerical_dataset': {'parameters': [['lambda', 'f']], 'attributes': ['S11', 'S12', 'S21', 'S22']},
            'wavelengths': np.array([1.55])
        }

    def save_simulation_file(self, filename: str) -> bool:
        """Save the current FDTD simulation file to the FDTD directory.

        Args:
            filename: Name of the file to save (e.g., 'particle_1_fitness_0.123456.fsp')

        Returns:
            bool: True if file was saved successfully, False otherwise
        """
        try:
            # Ensure FDTD directory exists and is writable
            self._ensure_fdtd_directory()

            if self.use_real_fdtd and self.session:
                # Save real FDTD simulation file
                file_path = self.fdtd_dir / filename

                # Check if we can write to the directory
                if not self._check_write_permission(file_path.parent):
                    print(f"❌ No write permission to directory: {file_path.parent}")
                    print(f"💡 Solution: Run as administrator or change directory permissions")
                    return False

                # Use absolute path for Lumerical save
                abs_path = file_path.resolve()

                # Convert to string with forward slashes (Lumerical prefers this)
                save_path = str(abs_path).replace('\\', '/')

                print(f"🔄 Saving FDTD file to: {save_path}")

                # Try to save with Lumerical
                self.session.save(save_path)

                # Wait a moment for file system to update
                import time
                time.sleep(0.1)

                # Verify file was created
                if abs_path.exists():
                    file_size = abs_path.stat().st_size
                    print(f"✓ FDTD file saved: {filename} ({file_size} bytes)")
                    return True
                else:
                    print(f"❌ File not created: {filename}")
                    print(f"💡 Attempted path: {save_path}")

                    # Try alternative save method
                    try:
                        # Try with Windows-style path
                        win_path = str(abs_path)
                        print(f"🔄 Trying Windows path: {win_path}")
                        self.session.save(win_path)
                        time.sleep(0.1)

                        if abs_path.exists():
                            print(f"✓ FDTD file saved with Windows path: {filename}")
                            return True
                    except Exception as e2:
                        print(f"❌ Alternative save method failed: {e2}")

                    return False
            else:
                # For mock simulations, create a placeholder file
                file_path = self.fdtd_dir / filename.replace('.fsp', '.txt')

                # Check write permission
                if not self._check_write_permission(file_path.parent):
                    print(f"❌ No write permission to directory: {file_path.parent}")
                    return False

                with open(file_path, 'w') as f:
                    f.write(f"Mock simulation file\nSimulation count: {self.simulation_count}\n")
                print(f"✓ Mock file saved: {file_path.name}")
                return True

        except PermissionError as e:
            print(f"❌ Permission denied saving {filename}: {e}")
            print(f"💡 Solution: Run as administrator or check directory permissions")
            return False
        except Exception as e:
            print(f"❌ Error saving simulation file {filename}: {e}")
            print(f"💡 Check if Lumerical session is active and directory is accessible")
            return False

    def _ensure_fdtd_directory(self):
        """Ensure FDTD directory exists and is accessible."""
        try:
            self.fdtd_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            print(f"❌ Cannot create FDTD directory: {self.fdtd_dir}")
            print(f"💡 Run as administrator or choose a different directory")
            raise

    def _check_write_permission(self, directory: Path) -> bool:
        """Check if we have write permission to a directory."""
        try:
            # Try to create a temporary file
            test_file = directory / "test_write_permission.tmp"
            test_file.touch()
            test_file.unlink()  # Clean up
            return True
        except (PermissionError, OSError):
            return False

    def get_insertion_loss(self, sp: Dict) -> float:
        """Extract insertion loss from S-parameters."""
        try:
            s21 = sp['S21'][0]
            transmission = abs(s21)**2
            loss_db = -10 * np.log10(transmission)
            return loss_db
        except:
            return 10.0  # High penalty for invalid results

    def close(self):
        """Close FDTD session."""
        if self.session:
            try:
                self.session.close()
                print("✓ FDTD session closed")
            except:
                pass
    

    

def create_fdtd_objective_function(use_real_fdtd: bool = False):
    """Create objective function for PSO optimization."""
    fdtd_interface = FDTDInterface(use_real_fdtd=use_real_fdtd)

    def objective_function(params: Dict[str, float]) -> float:
        """Objective function that returns insertion loss."""
        sp = fdtd_interface.simulate(params)
        return fdtd_interface.get_insertion_loss(sp)

    # Store interface reference for cleanup
    objective_function.fdtd_interface = fdtd_interface
    return objective_function
