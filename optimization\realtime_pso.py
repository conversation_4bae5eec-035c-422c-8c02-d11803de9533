"""
PSO with simplified, responsive real-time visualization.
Refactored for better maintainability and fixed FOM distribution data handling.
"""

import numpy as np
import matplotlib
import sys
import time
import threading
import queue
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, List, Callable

# Setup interactive backend
try:
    matplotlib.use('Qt5Agg', force=True)
    print("✓ Using Qt5Agg backend for interactive display")
except ImportError:
    try:
        matplotlib.use('TkAgg', force=True)
        print("✓ Using TkAgg backend for interactive display")
    except ImportError:
        print("⚠ Using default backend - visualization may not display properly")

import matplotlib.pyplot as plt


class BasePSOVisualizer:
    """Base class for PSO visualization with common functionality."""
    
    def __init__(self, param_names: List[str]):
        self.param_names = param_names
        self.iteration_data = []
        self.fitness_history = []
        self.param_history = {name: [] for name in param_names}
        
    def _setup_figure(self, title_suffix: str = ""):
        """Setup the main figure and grid layout."""
        plt.ion()
        
        # Verify interactive backend
        backend = matplotlib.get_backend()
        if 'Agg' in backend and backend != 'Qt5Agg':
            print(f"Warning: Using non-interactive backend {backend}")
        else:
            print(f"✓ Using interactive backend: {backend}")
        
        # Create figure
        self.fig = plt.figure(figsize=(15, 10))
        self.fig.patch.set_facecolor('white')
        self.fig.suptitle(f'PSO Optimization - Real-time Monitoring{title_suffix}',
                         fontsize=18, fontweight='bold', color='#2E4057')
        
        # Create 2x2 grid layout
        return self.fig.add_gridspec(2, 2, hspace=0.35, wspace=0.25,
                                   left=0.08, right=0.95, top=0.92, bottom=0.08)
    
    def _setup_all_plots(self, gs):
        """Setup all four plots using the grid."""
        # 1. FOM Evolution Plot (top left)
        self.ax_fom = self.fig.add_subplot(gs[0, 0])
        self.fom_line, = self.ax_fom.plot([], [], color='#1f77b4', linewidth=3,
                                         marker='o', markersize=5, markerfacecolor='white',
                                         markeredgecolor='#1f77b4', markeredgewidth=2)
        self.ax_fom.set_xlabel('Iteration', fontsize=12, fontweight='bold')
        self.ax_fom.set_ylabel('Best FOM (Fitness)', fontsize=12, fontweight='bold')
        self.ax_fom.set_title('FOM Evolution', fontsize=14, fontweight='bold', color='#2E4057')
        self.ax_fom.grid(True, alpha=0.4, linestyle='--')
        self.ax_fom.tick_params(labelsize=10)
        
        # 2. Current Best Parameters Display (top right)
        self.ax_params_text = self.fig.add_subplot(gs[0, 1])
        self.ax_params_text.axis('off')
        self.ax_params_text.set_title('Current Best Parameters', fontsize=14,
                                     fontweight='bold', color='#2E4057')
        self.param_text = self.ax_params_text.text(0.05, 0.95, '',
                                                  transform=self.ax_params_text.transAxes,
                                                  fontsize=12, verticalalignment='top',
                                                  fontfamily='monospace',
                                                  bbox=dict(boxstyle='round,pad=0.5', 
                                                           facecolor='#f0f8ff', alpha=0.8))
        
        # 3. Parameter Evolution Plot (bottom left)
        self.ax_param_evolution = self.fig.add_subplot(gs[1, 0])
        self.param_lines = {}
        colors = ['#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
        
        for i, param_name in enumerate(self.param_names):
            color = colors[i % len(colors)]
            line, = self.ax_param_evolution.plot([], [], color=color, linewidth=2,
                                                marker='s', markersize=4, label=param_name)
            self.param_lines[param_name] = line
        
        self.ax_param_evolution.set_xlabel('Iteration', fontsize=12, fontweight='bold')
        self.ax_param_evolution.set_ylabel('Parameter Value', fontsize=12, fontweight='bold')
        self.ax_param_evolution.set_title('Parameter Evolution', fontsize=14, fontweight='bold', color='#2E4057')
        self.ax_param_evolution.legend(fontsize=10, framealpha=0.9)
        self.ax_param_evolution.grid(True, alpha=0.4, linestyle='--')
        self.ax_param_evolution.tick_params(labelsize=10)
        
        # 4. FOM Distribution Plot (bottom right)
        self.ax_fom_dist = self.fig.add_subplot(gs[1, 1])
        self.ax_fom_dist.set_xlabel('FOM (Fitness) Value', fontsize=12, fontweight='bold')
        self.ax_fom_dist.set_ylabel('Frequency', fontsize=12, fontweight='bold')
        self.ax_fom_dist.set_title('FOM Distribution', fontsize=14, fontweight='bold', color='#2E4057')
        self.ax_fom_dist.grid(True, alpha=0.4, linestyle='--')
        self.ax_fom_dist.tick_params(labelsize=10)
    
    def _store_data(self, iteration: int, best_fitness: float, best_params: Dict[str, float]):
        """Store iteration data for plotting."""
        self.iteration_data.append(iteration)
        self.fitness_history.append(best_fitness)
        
        for param_name, value in best_params.items():
            if param_name in self.param_history:
                self.param_history[param_name].append(value)
    
    def _update_all_plots(self, iteration: int, best_fitness: float, best_params: Dict[str, float], all_fitness: List[float]):
        """Update all plots with current data."""
        # 1. Update FOM Evolution
        if self.fitness_history:
            iterations = list(range(len(self.fitness_history)))
            self.fom_line.set_data(iterations, self.fitness_history)
            self.ax_fom.relim()
            self.ax_fom.autoscale_view()
        
        # 2. Update Parameters Display
        param_text = f"Iteration: {iteration}\n"
        param_text += f"Best FOM: {best_fitness:.6f}\n\n"
        param_text += "Best Parameters:\n"
        for name, value in best_params.items():
            param_text += f"  {name}: {value:.4f}\n"
        self.param_text.set_text(param_text)
        
        # 3. Update Parameter Evolution
        for param_name, line in self.param_lines.items():
            if param_name in self.param_history and self.param_history[param_name]:
                iterations = list(range(len(self.param_history[param_name])))
                line.set_data(iterations, self.param_history[param_name])
        self.ax_param_evolution.relim()
        self.ax_param_evolution.autoscale_view()
        
        # 4. Update FOM Distribution (FIXED DATA HANDLING)
        self._update_fom_distribution(iteration, best_fitness, all_fitness)
    
    def _update_fom_distribution(self, iteration: int, best_fitness: float, all_fitness: List[float]):
        """Update FOM distribution plot with proper data validation."""
        self.ax_fom_dist.clear()
        
        # Validate and filter fitness data
        if not all_fitness:
            print(f"Warning: No fitness data for iteration {iteration}")
            self._setup_empty_fom_distribution(iteration)
            return
            
        # Convert to numpy array and filter out invalid values
        fitness_array = np.array(all_fitness)
        valid_fitness = fitness_array[np.isfinite(fitness_array)]
        
        if len(valid_fitness) == 0:
            print(f"Warning: No valid fitness data for iteration {iteration}")
            self._setup_empty_fom_distribution(iteration)
            return
        
        # Debug information
        print(f"Debug: FOM distribution for iteration {iteration}: {len(valid_fitness)} particles, "
              f"range [{np.min(valid_fitness):.4f}, {np.max(valid_fitness):.4f}]")
        
        # Create histogram
        n_bins = min(15, max(3, len(valid_fitness) // 2))
        self.ax_fom_dist.hist(valid_fitness, bins=n_bins,
                             alpha=0.8, color='#87CEEB',
                             edgecolor='#4682B4', linewidth=1.5)
        
        # Add best fitness line
        self.ax_fom_dist.axvline(best_fitness, color='red', linestyle='--',
                               linewidth=2, alpha=0.8,
                               label=f'Best: {best_fitness:.4f}')
        
        # Update labels and styling
        self.ax_fom_dist.set_xlabel('FOM (Fitness) Value', fontsize=12, fontweight='bold')
        self.ax_fom_dist.set_ylabel('Frequency', fontsize=12, fontweight='bold')
        self.ax_fom_dist.set_title(f'FOM Distribution - Iteration {iteration}',
                                 fontsize=14, fontweight='bold', color='#2E4057')
        self.ax_fom_dist.legend(fontsize=10, framealpha=0.9)
        self.ax_fom_dist.grid(True, alpha=0.4, linestyle='--')
        self.ax_fom_dist.tick_params(labelsize=10)
    
    def _setup_empty_fom_distribution(self, iteration: int):
        """Setup empty FOM distribution plot when no data is available."""
        self.ax_fom_dist.text(0.5, 0.5, 'No fitness data available',
                             transform=self.ax_fom_dist.transAxes,
                             ha='center', va='center', fontsize=14, alpha=0.7)
        self.ax_fom_dist.set_xlabel('FOM (Fitness) Value', fontsize=12, fontweight='bold')
        self.ax_fom_dist.set_ylabel('Frequency', fontsize=12, fontweight='bold')
        self.ax_fom_dist.set_title(f'FOM Distribution - Iteration {iteration}',
                                 fontsize=14, fontweight='bold', color='#2E4057')
        self.ax_fom_dist.grid(True, alpha=0.4, linestyle='--')
        self.ax_fom_dist.tick_params(labelsize=10)
    
    def _refresh_gui(self):
        """Refresh the GUI display."""
        self.fig.canvas.draw()
        self.fig.canvas.flush_events()


class ResponsivePSOVisualizer(BasePSOVisualizer):
    """PSO visualization with GUI responsiveness during long FDTD simulations."""

    def __init__(self, param_names: List[str]):
        super().__init__(param_names)
        self.setup_plots()

    def setup_plots(self):
        """Setup real-time visualization plots with GUI responsiveness."""
        print("Setting up real-time PSO visualization...")

        # Setup figure and all plots
        gs = self._setup_figure()
        self._setup_all_plots(gs)

        # Initial display
        plt.show(block=False)
        plt.pause(0.1)

        print("✓ Real-time visualization window created!")
        print("  📈 FOM Evolution | 📋 Best Parameters")
        print("  📊 Parameter Evolution | 📊 FOM Distribution")
        print("  🔄 GUI remains responsive during FDTD simulations")

    def update(self, iteration: int, best_fitness: float, best_params: Dict[str, float],
               all_particles: np.ndarray, all_fitness: List[float]):
        """Update all plots with enhanced responsiveness."""
        try:
            # Store data and update plots
            self._store_data(iteration, best_fitness, best_params)
            self._update_all_plots(iteration, best_fitness, best_params, all_fitness)
            
            # Enhanced GUI responsiveness
            self._refresh_gui()
            for _ in range(3):
                self.fig.canvas.flush_events()
                time.sleep(0.01)
            
            print(f"  Iteration {iteration}: FOM = {best_fitness:.6f}, Plots updated ✓")

        except Exception as e:
            print(f"Visualization update error: {e}")

    def update_status(self, status_text: str):
        """Update status text on the visualization."""
        try:
            self.fig.suptitle(f'PSO Optimization - Real-time Monitoring - {status_text}',
                             fontsize=18, fontweight='bold', color='#2E4057')
            self._refresh_gui()
        except Exception as e:
            print(f"Status update error: {e}")

    def close(self):
        """Close the visualization."""
        print("\nVisualization complete!")
        print("You can interact with the plots (zoom, pan, etc.)")
        try:
            input("Press Enter to close the visualization window...")
        except (KeyboardInterrupt, EOFError):
            print("Closing visualization...")
        finally:
            plt.close(self.fig)
            plt.ioff()





class RealtimePSO:
    """PSO with real-time visualization and threaded FDTD evaluation for GUI responsiveness."""

    def __init__(self, param_bounds: Dict[str, tuple], objective_func: Callable,
                 num_particles: int = 20, max_iterations: int = 50,
                 w: float = 0.7, c1: float = 1.5, c2: float = 1.5,
                 max_workers: int = 1, **kwargs):

        self.param_bounds = param_bounds
        self.objective_func = objective_func
        self.num_particles = num_particles
        self.max_iterations = max_iterations
        self.w = w  # Inertia weight
        self.c1 = c1  # Cognitive parameter
        self.c2 = c2  # Social parameter
        self.max_workers = max_workers

        # Handle backward compatibility for use_responsive_viz parameter
        if 'use_responsive_viz' in kwargs:
            print("Note: use_responsive_viz parameter is deprecated. Responsive visualization is always enabled.")

        # Extract parameter names and bounds
        self.param_names = list(param_bounds.keys())
        self.bounds = np.array([param_bounds[name] for name in self.param_names])
        self.num_dimensions = len(self.param_names)

        # Initialize particles
        self.positions = np.random.uniform(
            self.bounds[:, 0], self.bounds[:, 1],
            (self.num_particles, self.num_dimensions)
        )
        self.velocities = np.zeros((self.num_particles, self.num_dimensions))

        # Initialize best positions and fitness
        self.personal_best_positions = self.positions.copy()
        self.personal_best_fitness = np.full(self.num_particles, float('inf'))
        self.global_best_position = self.positions[0].copy()
        self.global_best_fitness = float('inf')

        # Setup visualization with GUI responsiveness
        self.visualizer = ResponsivePSOVisualizer(self.param_names)
        print("✓ Real-time visualization enabled (GUI remains interactive during FDTD)")

        # Setup FDTD file tracking system
        self._setup_fdtd_tracking()

    def _setup_fdtd_tracking(self):
        """Setup FDTD file tracking system."""
        # Create FDTD directory
        self.fdtd_dir = Path("optimization/FDTD")
        self.fdtd_dir.mkdir(parents=True, exist_ok=True)

        # Initialize tracking variables
        self.fdtd_interface = None  # Will be set when objective function is called
        self.current_particle_files = {}  # Maps particle index to filename
        self.best_fdtd_file = None  # Current best FDTD file

        print(f"✓ FDTD tracking initialized: {self.fdtd_dir}")

    def set_fdtd_interface(self, fdtd_interface):
        """Set the FDTD interface for file operations."""
        self.fdtd_interface = fdtd_interface

    def _save_particle_file(self, particle_idx: int, fitness: float) -> str:
        """Save FDTD file for a specific particle evaluation."""
        if self.fdtd_interface is None:
            return None

        filename = f"particle_{particle_idx}_fitness_{fitness:.6f}.fsp"
        success = self.fdtd_interface.save_simulation_file(filename)

        if success:
            self.current_particle_files[particle_idx] = filename
            return filename
        return None

    def _promote_to_best(self, particle_idx: int, fitness: float):
        """Promote a particle's FDTD file to be the new best result."""
        if particle_idx not in self.current_particle_files:
            return

        old_filename = self.current_particle_files[particle_idx]
        new_filename = f"best_simulation_fitness_{fitness:.6f}.fsp"

        try:
            # Get file paths
            old_path = self.fdtd_dir / old_filename
            new_path = self.fdtd_dir / new_filename

            # Handle different file extensions for mock vs real simulations
            if not old_path.exists():
                old_path = self.fdtd_dir / old_filename.replace('.fsp', '.txt')
                new_path = self.fdtd_dir / new_filename.replace('.fsp', '.txt')

            if old_path.exists():
                # Remove previous best file if it exists
                if self.best_fdtd_file:
                    prev_best_path = self.fdtd_dir / self.best_fdtd_file
                    if prev_best_path.exists():
                        prev_best_path.unlink()

                # Rename current particle file to best
                old_path.rename(new_path)
                self.best_fdtd_file = new_filename if new_path.suffix == '.fsp' else new_filename.replace('.fsp', '.txt')

                print(f"✓ New best FDTD file: {self.best_fdtd_file} (fitness: {fitness:.6f})")

                # Clean up other temporary files
                self._cleanup_temp_files(exclude_particle=particle_idx)

        except Exception as e:
            print(f"Warning: Could not promote FDTD file: {e}")

    def _cleanup_temp_files(self, exclude_particle: int = None):
        """Clean up temporary particle files to save disk space."""
        try:
            files_to_remove = []

            for particle_idx, filename in list(self.current_particle_files.items()):
                if exclude_particle is not None and particle_idx == exclude_particle:
                    continue

                # Don't remove the file that corresponds to the current best
                if self.best_fdtd_file and filename.replace('.fsp', '.txt') == self.best_fdtd_file:
                    continue

                files_to_remove.append((particle_idx, filename))

            # Remove the files
            for particle_idx, filename in files_to_remove:
                file_path = self.fdtd_dir / filename
                if not file_path.exists():
                    file_path = self.fdtd_dir / filename.replace('.fsp', '.txt')

                if file_path.exists():
                    file_path.unlink()

                # Remove from tracking
                if particle_idx in self.current_particle_files:
                    del self.current_particle_files[particle_idx]

            if files_to_remove:
                print(f"   🧹 Cleaned up {len(files_to_remove)} temporary FDTD files")

        except Exception as e:
            print(f"Warning: Error during cleanup: {e}")

    def params_to_dict(self, position: np.ndarray) -> Dict[str, float]:
        """Convert position array to parameter dictionary."""
        return {name: float(position[i]) for i, name in enumerate(self.param_names)}

    def evaluate_fitness(self, position: np.ndarray) -> float:
        """Evaluate fitness for a single particle position."""
        params = self.params_to_dict(position)
        fitness = self.objective_func(params)
        if not np.isfinite(fitness):
            return float('inf')  # Penalty for invalid fitness
        return fitness

    def evaluate_fitness_with_gui_responsiveness(self, position: np.ndarray) -> float:
        """Evaluate fitness while keeping GUI responsive using threading."""
        result_queue = queue.Queue()
        exception_queue = queue.Queue()

        def evaluation_worker():
            """Worker function that runs FDTD evaluation in background thread."""
            try:
                fitness = self.evaluate_fitness(position)
                result_queue.put(fitness)
            except Exception as e:
                exception_queue.put(e)

        # Start the evaluation in a background thread
        eval_thread = threading.Thread(target=evaluation_worker, daemon=True)
        eval_thread.start()

        # Keep GUI responsive while waiting for evaluation to complete
        while eval_thread.is_alive():
            # Process GUI events to keep visualization responsive
            if hasattr(self, 'visualizer') and hasattr(self.visualizer, 'fig'):
                self.visualizer.fig.canvas.flush_events()

            # Small sleep to prevent busy waiting
            time.sleep(0.1)

        # Get the result
        if not exception_queue.empty():
            raise exception_queue.get()

        return result_queue.get()

    def evaluate_fitness_batch(self, positions: List[np.ndarray]) -> List[float]:
        """Evaluate fitness for multiple positions with GUI responsiveness and FDTD file tracking."""
        if self.max_workers > 1:
            # Use threading for parallel evaluation (FDTD file saving not supported in parallel mode)
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(self.evaluate_fitness, pos) for pos in positions]
                return [future.result() for future in as_completed(futures)]
        else:
            # Use responsive evaluation that keeps GUI interactive
            results = []
            for i, pos in enumerate(positions):
                print(f"     Evaluating particle {i+1}/{len(positions)} (GUI responsive)...")
                print(f"     Parameters: {self.params_to_dict(pos)}")

                # Extract FDTD interface from objective function if available
                if hasattr(self.objective_func, 'fdtd_interface') and self.fdtd_interface is None:
                    self.set_fdtd_interface(self.objective_func.fdtd_interface)

                # Evaluate fitness
                fitness = self.evaluate_fitness_with_gui_responsiveness(pos)
                results.append(fitness)

                # Save FDTD file for this particle
                self._save_particle_file(i, fitness)

                print(f"     ✓ Particle {i+1} fitness: {fitness:.6f}")
            return results

    def update_velocity_and_position(self, particle_idx: int):
        """Update velocity and position for a single particle."""
        # Random factors
        r1, r2 = np.random.random(2)

        # Update velocity
        cognitive = self.c1 * r1 * (self.personal_best_positions[particle_idx] - self.positions[particle_idx])
        social = self.c2 * r2 * (self.global_best_position - self.positions[particle_idx])

        self.velocities[particle_idx] = (self.w * self.velocities[particle_idx] +
                                       cognitive + social)

        # Update position
        self.positions[particle_idx] += self.velocities[particle_idx]

        # Apply bounds
        for dim in range(self.num_dimensions):
            if self.positions[particle_idx, dim] < self.bounds[dim, 0]:
                self.positions[particle_idx, dim] = self.bounds[dim, 0]
                self.velocities[particle_idx, dim] = 0
            elif self.positions[particle_idx, dim] > self.bounds[dim, 1]:
                self.positions[particle_idx, dim] = self.bounds[dim, 1]
                self.velocities[particle_idx, dim] = 0

    def optimize(self) -> tuple:
        """Run PSO optimization with real-time visualization and GUI responsiveness."""
        print("="*60)
        print("STARTING PSO OPTIMIZATION WITH REAL-TIME VISUALIZATION")
        print("="*60)
        print(f"Parameters: {self.param_names}")
        print(f"Particles: {self.num_particles}")
        print(f"Max iterations: {self.max_iterations}")
        print(f"Real-time visualization: Enabled")
        print(f"Threading workers: {self.max_workers}")

        # Initial evaluation
        print("\n🔄 Initial particle evaluation...")
        current_fitness = self.evaluate_fitness_batch([self.positions[i] for i in range(self.num_particles)])

        for i, fitness in enumerate(current_fitness):
            self.personal_best_fitness[i] = fitness
            if fitness < self.global_best_fitness:
                self.global_best_fitness = fitness
                self.global_best_position = self.positions[i].copy()
                # Promote this particle's FDTD file to best result
                self._promote_to_best(i, fitness)

        # Initial visualization update
        best_params = self.params_to_dict(self.global_best_position)
        self.visualizer.update(0, self.global_best_fitness, best_params,
                              self.positions, current_fitness)
        print(f"✓ Initial best fitness: {self.global_best_fitness:.6f}")

        # Main optimization loop
        for iteration in range(self.max_iterations):
            print(f"\n🔄 Iteration {iteration + 1}/{self.max_iterations}")

            # Update all particle positions
            for i in range(self.num_particles):
                self.update_velocity_and_position(i)

            # Evaluate all particles with GUI responsiveness
            print(f"   Evaluating {self.num_particles} particles...")
            start_time = time.time()

            # Update status to show FDTD evaluation is running
            self.visualizer.update_status(f"Running FDTD Simulations - Iteration {iteration + 1}")

            iteration_fitness = self.evaluate_fitness_batch([self.positions[i] for i in range(self.num_particles)])

            eval_time = time.time() - start_time
            print(f"   ✓ Evaluation completed in {eval_time:.1f}s")

            # Update status to show evaluation is complete
            self.visualizer.update_status("Processing Results")

            # Update personal and global bests
            for i, fitness in enumerate(iteration_fitness):
                # Update personal best
                if fitness < self.personal_best_fitness[i]:
                    self.personal_best_fitness[i] = fitness
                    self.personal_best_positions[i] = self.positions[i].copy()

                # Update global best
                if fitness < self.global_best_fitness:
                    self.global_best_fitness = fitness
                    self.global_best_position = self.positions[i].copy()
                    # Promote this particle's FDTD file to best result
                    self._promote_to_best(i, fitness)

            # Update visualization
            best_params = self.params_to_dict(self.global_best_position)
            self.visualizer.update(iteration + 1, self.global_best_fitness, best_params,
                                  self.positions, iteration_fitness)

            # Clean up temporary FDTD files from this iteration (keep only the best)
            self._cleanup_temp_files()

            # Print progress
            avg_fitness = np.mean(iteration_fitness)
            print(f"   📊 Best: {self.global_best_fitness:.6f}, Avg: {avg_fitness:.6f}")

            # Allow GUI to remain responsive
            time.sleep(0.1)

        # Final results
        best_params = self.params_to_dict(self.global_best_position)

        # Update status to show optimization is complete
        self.visualizer.update_status("Optimization Complete")

        print("\n" + "="*60)
        print("OPTIMIZATION COMPLETED")
        print("="*60)
        print(f"Best FOM: {self.global_best_fitness:.6f}")
        print(f"Best parameters:")
        for name, value in best_params.items():
            print(f"  {name}: {value:.6f}")

        # Keep visualization open
        self.visualizer.close()

        return best_params, self.global_best_fitness
