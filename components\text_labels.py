import gdsfactory as gf
from gdsfactory.typings import LayerSpec


@gf.cell
def simple_text_label(
    text: str = "Sample",
    size: float = 100.0,
    layer: LayerSpec = "TEXT",
) -> gf.Component:
    """创建简单的文本标签.
    
    Args:
        text: 要显示的文本.
        size: 文本大小.
        layer: 文本层.
    
    Returns:
        gf.Component: 文本标签组件.
    """
    return gf.components.text(
        text=text,
        size=size,
        layer=layer,
    )


if __name__ == "__main__":
    c = simple_text_label()
    c.show()