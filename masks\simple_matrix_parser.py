#!/usr/bin/env python3
"""
简单配置解析器 - 专门处理矩阵格式的简单配置文件
"""

import yaml
from pathlib import Path
from typing import Dict, Any, List

def parse_matrix_config(config_path: str) -> Dict[str, Any]:
    """解析矩阵格式的简单配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        chip = config['chip']
        pcell = config['pcell']
        layout = config['layout']
        parameter_sweep = config['parameter_sweep']
        
        # 构建标准格式配置
        standard_config = {
            'chip_settings': {
                'name': chip['name'],
                'size': chip['size'],
                'spacing': [layout['grid']['col_spacing'], layout['grid']['row_spacing']]
            },
            'smallparts_layout': {},
            'smallparts': {}
        }
        
        rows = layout['grid']['rows']
        cols = layout['grid']['cols']
        
        # 获取布局矩阵
        x_offsets = layout.get('x_offsets', [[0] * cols for _ in range(rows)])
        y_offsets = layout.get('y_offsets', [[0] * cols for _ in range(rows)])
        count_matrix = layout.get('count_matrix', [[pcell.get('count', 8)] * cols for _ in range(rows)])
        label_offsets = layout.get('label_offsets', [[30] * cols for _ in range(rows)])
        internal_spacing_matrix = layout.get('internal_spacing_matrix', [[-250] * cols for _ in range(rows)])
        
        # 生成每个smallpart的配置
        for row in range(rows):
            for col in range(cols):
                smallpart_name = f"smallpart{row * cols + col + 1}"
                pos_key = f"[{row}, {col}]"
                
                # 从矩阵获取布局参数
                x_offset = _get_matrix_value(x_offsets, row, col, 0)
                y_offset = _get_matrix_value(y_offsets, row, col, 0)
                count = _get_matrix_value(count_matrix, row, col, pcell.get('count', 8))
                label_offset = _get_matrix_value(label_offsets, row, col, 30)
                internal_spacing = _get_matrix_value(internal_spacing_matrix, row, col, -250)
                
                standard_config['smallparts_layout'][smallpart_name] = {
                    'row': row,
                    'col': col,
                    'label_offset': label_offset,
                    'x_offset': x_offset,
                    'y_offset': y_offset,
                    'grid': {
                        'spacing': [0, internal_spacing]
                    }
                }
                
                # 获取位置特定参数
                pos_params = parameter_sweep.get(pos_key, {})
                final_params = pcell['defaults'].copy()
                final_params.update(pos_params)
                
                standard_config['smallparts'][smallpart_name] = {
                    'pcell_name': pcell['type'],
                    'count': count,
                    'add_dicing_marks': False,
                    'dicing_offset_x': -100.0,
                    'dicing_offset_y': 100.0,
                    'parameters': final_params
                }
        
        # 添加标记配置（直接传递）
        if 'alignment_marks' in config:
            standard_config['alignment_marks'] = config['alignment_marks']
            
        if 'chip_dicing' in config:
            standard_config['chip_dicing'] = config['chip_dicing']
            
        if 'smallpart_dicing' in config:
            standard_config['smallpart_dicing'] = config['smallpart_dicing']
        
        return standard_config
        
    except Exception as e:
        print(f"❌ 解析矩阵配置失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def _get_matrix_value(matrix: List[List], row: int, col: int, default_value):
    """安全地从矩阵中获取值"""
    try:
        if row < len(matrix) and col < len(matrix[row]):
            return matrix[row][col]
        return default_value
    except:
        return default_value

def is_matrix_config(config_data: Dict[str, Any]) -> bool:
    """检测是否为矩阵格式配置"""
    if not isinstance(config_data, dict):
        return False
        
    # 检查关键结构
    required_keys = ['chip', 'pcell', 'layout', 'parameter_sweep']
    if not all(key in config_data for key in required_keys):
        return False
    
    # 检查pcell结构
    pcell = config_data.get('pcell', {})
    if not isinstance(pcell, dict) or 'type' not in pcell:
        return False
    
    # 检查layout结构中是否有矩阵
    layout = config_data.get('layout', {})
    matrix_indicators = ['x_offsets', 'y_offsets', 'count_matrix', 'label_offsets', 'internal_spacing_matrix']
    has_matrix = any(indicator in layout for indicator in matrix_indicators)
    
    return has_matrix

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
        result = parse_matrix_config(config_path)
        if result:
            print("✅ 解析成功!")
            print(f"📊 生成了 {len(result.get('smallparts', {}))} 个smallparts")
        else:
            print("❌ 解析失败!")
    else:
        print("用法: python simple_matrix_parser.py <config_file>")
