# 🐱 通用配置文件 - 多PCells类型混合专用

# ===== 芯片基本信息 =====
chip_settings:
  name: "parameter_sweep_chip"
  size: [12000.0, 12000.0]  # [宽度, 高度] in um
  spacing: [0, 300]     # smallpart 网格的间距 (x, y)



# ===== PCells库定义 =====
pcells:
  ring_with_electrode_with_heater:
    component: "ring_with_electrode_with_heater_with_tip"
    defaults:
      ring_width: 1.5
      waveguide_width: 1
      gap: 0.6
      coupling_angle_coverage: 10
      ring_radius: 99.25
      heater_width: 3
      length_left: 100
      length_total: 600
    
  
      
  simple_electrode:
    component: "waveguide_tip_with_ppln_electrodes"
    defaults:
      wg_width: 2
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      bend_radius: 100
      offset: -300
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
      
  waveguide_tip:
    component: "waveguide_tip_with_ppln_electrodes"
    defaults:
      wg_width: 2
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      bend_radius: 100
      offset: -300
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
      
  custom_waveguide:
    component: "waveguide_tip_with_ppln_electrodes"
    defaults:
      wg_width: 2
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      bend_radius: 100
      offset: -300
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5

# ===== 布局设置（集中管理）=====
layout:
  # 网格设置
  grid:
    rows: 3                    # 行数（对应3个length_left变化）
    cols: 1                    # 列数  
    row_spacing: 300           # 行间距 µm
    col_spacing: 0             # 列间距 µm（紧挨着）
    
  # 标签设置
  labels:
    enabled: true
    format: "{col_letter}{row_number}"  # A1, B2格式
    default_offset: 30                  # 默认标签偏移
    
  # smallpart内部布局
  internal:
    default_spacing: 0      # 默认smallpart内部组件间距
    default_count: 3           # 默认每个smallpart的组件数量
    
  # 列定义（按列组织PCells类型和参数）
  columns:
    # 第1列：波导长度扫描
    col_0:
      pcell_type: ring_with_electrode_with_heater
      description: ""
      base_params: {}
      variations:
        - {length_left: 300}
        - {length_left: 200}
        - {length_left: 300}
      position_offset: [0, 0]
        
    # 第2列：极化长度扫描
    col_1:
      pcell_type: ring_with_electrode_with_heater
      description: "极化长度扫描"
      base_params: {}
      variations:
        
      position_offset: [0, 0]
        
    # 第3列：占空比扫描
    col_2:
      pcell_type: "waveguide_tip"
      description: "占空比扫描"
      base_params: {wg_length: 1200, poling_length: 1000, gap_to_wg: 5.0, offset: -300}
      variations:
        - {duty_cycle: 0.2}
        - {duty_cycle: 0.25}
        - {duty_cycle: 0.3}
      position_offset: [-100, 0]
        
    # 第4列：电极间距扫描
    col_3:
      pcell_type: "waveguide_tip"
      description: "电极间距扫描"
      base_params: {wg_length: 1200, poling_length: 1000, duty_cycle: 0.25, offset: -300}
      variations:
        - {gap_to_wg: 5.0}
        - {gap_to_wg: 7.5}
        - {gap_to_wg: 10.0}
      position_offset: [-200, 0]
        
    # 第5列：简单电极
    col_4:
      pcell_type: "simple_electrode"
      description: "电极参数扫描"
      base_params: {gap_to_wg: 10, bus_padding: 5}
      variations:
        - {wg_width: 1, bus_width: 15}
        - {wg_width: 2, bus_width: 20}
        - {wg_width: 3, bus_width: 25}
      position_offset: [-300, 0]
      layout_override: {count: 6, label_offset: 50}
        
    # 第6列：自定义波导
    col_5:
      pcell_type: "custom_waveguide"
      description: "波导长度扫描"
      base_params: {wg_width: 2, poling_length: 800}
      variations:
        - {wg_length: 800}
        - {wg_length: 1200}
        - {wg_length: 1600}
      position_offset: [-400, 0]
      layout_override: {count: 4, label_offset: 80, internal_spacing: -100}

# ===== 标记设置 =====
marks:
  # 对准标记
  alignment:
    enabled: true
    layer: "MARK"
    margins: [1500, 1000, 1000, 1500]  # [xx, xy, yx, yy]
    spacing: 1000
    cross_size: [50, 2]  # [length, width]
    
  # 切割标记
  dicing:
    smallpart_enabled: true
    chip_enabled: true
    layer: "DICING"
    mark_size: [50, 20]
    boundary_offset: [-200, 200]

# ===== 输出设置 =====
output:
  path: "mixed_pcells_chip.gds"
  show_gds: true
