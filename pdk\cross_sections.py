import gdsfactory as gf
from gdsfactory.cross_section import  xsection
from gdsfactory.typings import LayerSpec
from .layers import LAYER
from gdsfactory.cross_section import CrossSection, cross_section, xsection
from gdsfactory.typings import LayerSpec
@xsection
def create_wg_with_electrodes_cs(
    wg_width: float = 0.5,
    electrode_width: float = 10.0,
    gap: float = 3.0,
) -> gf.CrossSection:
    """Returns a gdsfactory.CrossSection for a waveguide with electrodes.

    Args:
        wg_width: width of the waveguide.
        electrode_width: width of the electrodes.
        gap: gap between the waveguide and the electrodes.
    """
    s_wg = gf.Section(name="waveguide", width=wg_width, layer=LAYER.WG, offset=0)

    offset_electrode = wg_width / 2 + gap + electrode_width / 2
    s_electrode1 = gf.Section(
        name="electrode1",
        width=electrode_width,
        offset=offset_electrode,
        layer=LAYER.METAL,
    )
    s_electrode2 = gf.Section(
        name="electrode2",
        width=electrode_width,
        offset=-offset_electrode,
        layer=LAYER.METAL,
    )

    xs = gf.CrossSection(sections=(s_wg, s_electrode1, s_electrode2))
    return xs

@xsection
def strip_waveguide(
    width: float = 0.5,
    layer: LayerSpec = LAYER.WG,
) -> gf.CrossSection:
    """Returns a simple strip waveguide cross-section.
    
    Args:
        width: width of the waveguide.
        layer: Specific layer to use for the waveguide.
    """
    s_wg = gf.Section(name="waveguide", width=width, layer=layer, offset=0)
    xs = gf.CrossSection(sections=(s_wg,))
    return xs

@xsection
def strip(
    width: float = 3,
    layer: LayerSpec = (2, 0),
    radius: float = 10.0,
    radius_min: float = 5,
    **kwargs,
) -> CrossSection:
    """Return Strip cross_section."""
    return cross_section(
        width=width,
        layer=layer,
        radius=radius,
        radius_min=radius_min,
        **kwargs,
    )

@xsection
def metal_routing(
    width: float = 10.0,
    layer: LayerSpec = LAYER.METAL,
    radius: float = 10.0,
    radius_min: float = 5,
    **kwargs,
) -> CrossSection:
    """Return metal routing cross_section for electrical connections."""
    return cross_section(
        width=width,
        layer=layer,
        radius=radius,
        radius_min=radius_min,
        **kwargs,
    )