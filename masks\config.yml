# 芯片组装配置文件

# 芯片的全局设置
chip_settings:
  name: "parameter_sweep_chip"
  size: [12000.0, 12000.0]  # [宽度, 高度] in um
  spacing: [0, 300]     # smallpart 网格的间距 (x, y)

# Smallparts 在芯片上的布局定义
smallparts_layout:
  smallpart1:
    row: 0
    col: 0
    label_offset: 30
    x_offset: 200.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 000.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [000,-250]
  smallpart2:
    row: 0
    col: 1
    label_offset: 30
    x_offset: 0.0    # 整体X方向偏移量 (相对于网格位置)
    y_offset: 00.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [000, -250]
  smallpart3:
    row: 0
    col: 2
    label_offset: 30
    x_offset: -200.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0   # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [000, -250]
  smallpart4:
    row: 0
    col: 3
    label_offset: 100
    x_offset: -400.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [000, -50]
  smallpart5:
    row: 1
    col: 0
    label_offset: 30
    x_offset: 200.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [000, -250]
  smallpart6:
    row: 1
    col: 1
    label_offset: 30
    x_offset: 0.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [000, -250]
  smallpart7:
    row: 1
    col: 2
    label_offset: 30
    x_offset: -200.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [000, -250]
  smallpart8:
    row: 1
    col: 3
    label_offset: 100
    x_offset: -400.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [00, -50]
  smallpart9:
    row: 2
    col: 0
    label_offset: 30
    x_offset: 200.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [00, -250]
  smallpart10:
    row: 2
    col: 1
    label_offset: 30
    x_offset: 0.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 00.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [00, -250]
  smallpart11:
    row: 2
    col: 2
    label_offset: 30
    x_offset: -200.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0.0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [00, -250]
  smallpart12:
    row: 2
    col: 3
    label_offset: 100
    x_offset: -400.0     # 整体X方向偏移量 (相对于网格位置)
    y_offset: 0     # 整体Y方向偏移量 (相对于网格位置)
    grid:
      spacing: [0, -50]
# SmallPart 详细配置
smallparts:
  smallpart1:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 800 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 500.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
      
  smallpart2:
    pcell_name: "waveguide_tip_with_ppln_electrodes"  # 多参数扫描测试
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100
    dicing_offset_y: 100
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5

  smallpart3:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
       # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 7.5
      bus_padding: 5

  smallpart4:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
       # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: 0
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1500.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5
      bus_padding: 5
  smallpart5:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 800 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 500.0
      period: 4.66
      duty_cycle: 0.25
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
  smallpart6:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.25
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5

  smallpart7:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.25
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 7.5
  smallpart8:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 8
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
         # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: 0
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1500.0
      period: 4.66
      duty_cycle: 0.25
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5
      bus_padding: 5
  smallpart9:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 6
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 800 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 500.0
      period: 4.66
      duty_cycle: 0.3
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
  smallpart10:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 6
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.3
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
  smallpart11:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 6
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
      # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: -300
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.3
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 7.5
      bus_padding: 5
  smallpart12:
    pcell_name: "waveguide_tip_with_ppln_electrodes"
    count: 6
    add_dicing_marks: false # 是否为此smallpart添加切割标记
    dicing_offset_x: -100.0
    dicing_offset_y: 100.0
    parameters:
        # Waveguide parameters
      wg_width: 2
      wg_length: 1200 # 中央波导长度，如果为None则自动计算
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      offset: 0
      bend_radius: 100
    
      # Electrode parameters
      poling_length: 1500.0
      period: 4.66
      duty_cycle: 0.3
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5
      bus_padding: 5
  
# 对准标记配置
alignment_marks:
  enabled: true  # 是否添加对准标记
  layer: "MARK"  # 对准标记所在的图层名称
  margin_xx: 1500.0  # X方向标记与左右边缘的距离
  margin_xy: 1000.0  # X方向标记与上下边缘的距离
  margin_yx: 1000.0  # Y方向标记与左右边缘的距离
  margin_yy: 1500.0  # Y方向标记与上下边缘的距离
  spacing: 1000.0   # 标记之间的间距
  cross_length: 50.0  # 十字臂的长度
  cross_width: 2.0    # 十字臂的宽度

# Smallpart切割标记配置
smallpart_dicing:
  enabled: true
  layer: "DICING"
  size: [50, 20] # [width, height]

# 芯片级切割标记配置 (新增)
chip_dicing:
  enabled: true               # 是否启用芯片级切割标记
  layer: "DICING"            # 切割标记图层
  mark_width: 50.0           # 切割标记宽度 (µm)
  mark_height: 20.0          # 切割标记高度 (µm)
  boundary_xoffset: -200.0     # 边界偏移距离 (µm)
  boundary_yoffset: 200.0     # 边界偏移距离 (µm)

# 芯片物理尺寸配置
chip_size:
  width: 12000.0  # 芯片宽度 (微米)
  height: 12000.0  # 芯片高度 (微米)
  draw_boundary: true  # 是否绘制芯片边界
  boundary_layer: "SUBSTRATE"  # 边界层名称






   
