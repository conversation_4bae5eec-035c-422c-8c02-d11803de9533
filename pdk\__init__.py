"""PDK package for GDS cursor."""

import gdsfactory as gf
import inspect
from pathlib import Path
import importlib

from gdsfactory.typings import Layer, LayerSpec, ComponentSpec, CrossSectionSpec
from gdsfactory.component import Component
from gdsfactory.technology import LayerViews, LayerView

# 从相应模块导入定义
from pdk.layers import LAYER
from pdk import cross_sections

# -----------------
# 组件发现函数
# -----------------
def get_component_factories():
    """
    自动发现和导入所有组件。简单而准确！
    """
    import os
    import importlib
    
    component_factories = {}
    components_dir = Path(__file__).parent.parent / "components"
    
    print("🔍 自动扫描components目录...")
    
    # 扫描components目录下的所有Python文件
    for py_file in components_dir.glob("*.py"):
        if py_file.name.startswith("__"):
            continue  # 跳过__init__.py等特殊文件
            
        module_name = py_file.stem
        print(f"  📦 检查模块: {module_name}")
        
        try:
            # 动态导入模块
            module = importlib.import_module(f"components.{module_name}")
            
            # 查找所有可调用的函数（更宽泛的检测）
            for attr_name in dir(module):
                if attr_name.startswith('_'):
                    continue  # 跳过私有函数
                    
                attr = getattr(module, attr_name)
                
                # 检查是否是函数
                if callable(attr) and hasattr(attr, '__module__'):
                    # 确保这个函数是在当前模块定义的，不是导入的
                    if attr.__module__ == f"components.{module_name}":
                        component_factories[attr_name] = attr
                        print(f"    ✅ 发现组件: {attr_name}")
                    
        except Exception as e:
            print(f"    ⚠️  模块 {module_name} 导入失败: {e}")
            continue
    
    print(f"🎉 自动发现了 {len(component_factories)} 个组件!")
    for name in sorted(component_factories.keys()):
        print(f"    - {name}")
    
    return component_factories

# --- 创建一个干净的 LayerViews ---
# 这可以防止gdsfactory加载默认的、包含很多层的视图
layer_views = LayerViews()

# 定义层的颜色和样式
layer_colors = {
    'SUBSTRATE': '#c0c0c0',
    'WG': '#4040ff', 
    'METAL': '#ffd400',
    'HEATER': '#ff8080',
    'DICING': '#80c080',
    'TEXT': '#804080',
    'MARK': '#c0c040'
}

# 只添加我们自己定义的层到视图中
defined_layers = []

# 直接使用我们知道的层定义
layer_definitions = {
    'SUBSTRATE': (0, 0),
    'WG': (1, 0),
    'METAL': (41, 0),
    'HEATER': (49, 0),
    'DICING': (5, 0),
    'TEXT': (66, 0),
    'MARK': (101, 0)
}

for layer_name, layer_value in layer_definitions.items():
    color = layer_colors.get(layer_name, '#808080')  # 默认灰色
    layer_views.layer_views[layer_name] = LayerView(
        layer=layer_value,
        color=color
    )
    defined_layers.append(layer_name)

print(f"Defined layers found: {defined_layers}")

# 获取默认PDK的配置作为基础
default_pdk = gf.get_active_pdk()
print(f"继承默认PDK '{default_pdk.name}' 的配置...")

# 获取默认PDK的其他配置（排除我们要自定义的部分）
default_config = {k: v for k, v in default_pdk.__dict__.items() 
                  if k not in ['name', 'layers', 'layer_views', 'cross_sections', 'cells'] 
                  and not k.startswith('_')}

# 确保bend_points_distance是数值
if 'bend_points_distance' in default_config:
    if isinstance(default_config['bend_points_distance'], str):
        try:
            default_config['bend_points_distance'] = float(default_config['bend_points_distance'])
        except:
            default_config['bend_points_distance'] = 0.02  # 默认值
else:
    default_config['bend_points_distance'] = 0.02  # 确保存在

print(f"继承的默认配置: {list(default_config.keys())}")
print(f"bend_points_distance: {default_config['bend_points_distance']} (type: {type(default_config['bend_points_distance'])})")

# 创建自定义PDK，继承默认PDK的所有配置
PDK = gf.Pdk(
    name="MyPDK",
    # 使用我们自定义的配置
    layers=LAYER,
    layer_views=layer_views,
    
    # 合并cross_sections（默认的 + 自定义的）
    cross_sections={
        **getattr(default_pdk, 'cross_sections', {}),
        **{
            name: func
            for name, func in inspect.getmembers(cross_sections, inspect.isfunction)
            if inspect.signature(func).return_annotation == gf.CrossSection
        }
    },
    
    # 合并cells（默认的 + 自定义的）
    cells={
        **getattr(default_pdk, 'cells', {}),
        **get_component_factories(),
    },
    
    # 继承默认PDK的其他所有配置
    **default_config
)

# 确保bend_points_distance是正确的数值类型
PDK.bend_points_distance = 0.02

# 定义层转换，支持不同层之间的taper
from functools import partial

# 定义electrical taper用于METAL和HEATER层之间的转换
layer_transitions = {
    # HEATER层的taper
    (49, 0): partial(gf.components.taper_electrical,
                    layer=(49, 0),
                    length=10.0),
    # METAL层的taper
    (41, 0): partial(gf.components.taper_electrical,
                    layer=(41, 0),
                    length=10.0),
    # Layer 24的taper (默认PDK中的某个金属层)
    (24, 0): partial(gf.components.taper_electrical,
                    layer=(24, 0),
                    length=10.0),
    # HEATER到METAL的转换
    ((49, 0), (41, 0)): partial(gf.components.taper_electrical,
                               layer=(49, 0),  # 使用起始层
                               length=10.0),
    # METAL到HEATER的转换
    ((41, 0), (49, 0)): partial(gf.components.taper_electrical,
                               layer=(41, 0),  # 使用起始层
                               length=10.0),
    # Layer 24到HEATER的转换
    ((24, 0), (49, 0)): partial(gf.components.taper_electrical,
                               layer=(24, 0),
                               length=10.0),
    # HEATER到Layer 24的转换
    ((49, 0), (24, 0)): partial(gf.components.taper_electrical,
                               layer=(49, 0),
                               length=10.0),
}

# 更新PDK的layer_transitions
PDK.layer_transitions = layer_transitions

PDK.activate()

# 简化版的层检查
print(f"PDK activated with {len(defined_layers)} custom layers: {defined_layers}")

# 导出干净的层对象供全局使用
LAYERS = PDK.layers

__all__ = [
    "PDK",
    "LAYERS",
    "Layer",
    "LayerSpec",
    "Component",
    "ComponentSpec",
    "CrossSectionSpec",
]
