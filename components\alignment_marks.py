import gdsfactory as gf
import numpy as np
from gdsfactory.component import Component
from gdsfactory.typings import LayerSpec

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from pdk.layers import LAYER
except ImportError:
    # 如果PDK未找到，则定义虚拟图层
    print("警告: 无法从pdk.layers导入LAYER。将使用默认的虚拟图层。")
    class DummyLAYER:
        MARK = (101, 0)
    LAYER = DummyLAYER()


@gf.cell
def alignment_cross(
    length: float = 50.0,
    width: float = 2.0,
    layer: LayerSpec = LAYER.MARK,
) -> Component:
    """创建一个十字形的对准标记，作为单个多边形。

    Args:
        length: 十字每个臂的总长度。
        width: 十字臂的宽度。
        layer: 标记所在的GDS图层。

    Returns:
        一个包含单个十字形多边形的组件。
    """
    c = gf.Component()
    l2 = length / 2
    w2 = width / 2
    # 定义十字的12个顶点坐标，使其成为一个单一的多边形
    points = [
        (l2, w2),
        (w2, w2),
        (w2, l2),
        (-w2, l2),
        (-w2, w2),
        (-l2, w2),
        (-l2, -w2),
        (-w2, -w2),
        (-w2, -l2),
        (w2, -l2),
        (w2, -w2),
        (l2, -w2),
    ]
    c.add_polygon(points, layer=layer)
    rec = gf.components.rectangle(
        size=(length+10, length+10),
        layer=(2,1),
        centered=True
    )
    c<<rec
    return c


def add_alignment_marks(
    chip: Component,
    chip_width: float = 10000.0,
    chip_height: float = 10000.0,
    margin_xx: float = 500.0,
    margin_xy: float = 500.0,
    margin_yx: float = 500.0,
    margin_yy: float = 500.0,
    spacing: float = 1000.0,
    cross_length: float = 50.0,
    cross_width: float = 2.0,
    layer: LayerSpec = LAYER.MARK,
) -> None:
    """将周期性排列的十字对准标记添加到给定的芯片组件边缘。

    该函数假定芯片组件的中心位于(0, 0)。

    Args:
        chip: 要添加对准标记的gf.Component。
        chip_width: 芯片宽度 (µm)。
        chip_height: 芯片高度 (µm)。
        margin_xx: 标记中心距离上芯片边缘的距离。
        margin_xy: 标记中心距离下芯片边缘的距离。
        margin_yx: 标记中心距离左芯片边缘的距离。
        margin_yy: 标记中心距离右芯片边缘的距离。
        spacing: 标记沿x和y轴的周期性间距。
        cross_length: 十字臂的长度。
        cross_width: 十字臂的宽度。
        layer: 对准标记所在的GDS图层。
    """
    # 创建对准标记的主单元
    cross = alignment_cross(
        length=cross_length,
        width=cross_width,
        layer=layer,
    )
    # 芯片边界（假设中心为0,0）
    x_max = chip_width / 2
    x_min = -chip_width / 2
    y_max = chip_height / 2
    y_min = -chip_height / 2

    # --- 添加上、下边缘的标记（包括角落） ---
    y_top = y_max - margin_xy
    y_bottom = y_min + margin_yx
    
    x_positions = np.arange(x_min + margin_xx, x_max - margin_xx+spacing, spacing)
    
    for x in x_positions:
        chip.add_ref(cross).dmove((x, y_top))
        chip.add_ref(cross).dmove((x, y_bottom))

    # --- 添加左、右边缘的标记（不包括角落，因为它们已经被添加） ---
    x_left = x_min + margin_yx
    x_right = x_max - margin_yx
    
    # 从第二个标记开始，到倒数第二个标记结束
    y_positions = np.arange(y_min + margin_yy, y_max - margin_yy+spacing, spacing)
    
    for y in y_positions:
        chip.add_ref(cross).dmove((x_left, y))
        chip.add_ref(cross).dmove((x_right, y))


if __name__ == '__main__':
    # 示例用法:
    # 1. 创建一个顶层芯片组件
    my_chip = gf.Component("MyChip_With_Marks")

    # 2. 定义芯片尺寸
    chip_w, chip_h = 12000, 12000

    # 3. (可选) 添加芯片边界
    my_chip.add_ref(gf.components.rectangle(size=(chip_w, chip_h), layer=(100,0), centered=True))

    # 4. (可选) 添加一些其他内容到芯片
    main_content = gf.components.text("CHIP_CENTER")
    my_chip.add_ref(main_content).move(main_content.center)

    # 5. 调用函数添加对准标记
    print("正在添加对准标记...")
    add_alignment_marks(
        chip=my_chip,
        chip_width=chip_w,
        chip_height=chip_h,
        margin_xx=1500,
        margin_xy=1000,
        margin_yx=1000,
        margin_yy=1500,
        spacing=1000,    
        cross_length=50,
        cross_width=2,
    )
    print("✓ 对准标记添加完成。")
    
    # 6. 显示最终芯片
    my_chip.show()
