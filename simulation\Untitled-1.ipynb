import gdsfactory as gf
section_inner = gf.cross_section.cross_section(
    width=1.5,
    offset=0,
    layer=(1, 0)
)
section_outer = gf.cross_section.cross_section(
    width=2,
    offset=0,
    layer=(1, 0)
)

# 创建基础环形耦合器
coupler = gf.components.coupler_ring_bend(
    coupler_gap=0.5,
    radius=50,
    coupling_angle_coverage=20,
    length_x=0,
    cross_section_inner=section_inner,
    cross_section_outer=section_outer,
    bend_output="bend_euler",
    straight="straight",
)

# 创建新的组件
c = gf.Component()

# 添加耦合器
coupler_ref = c << coupler

# 添加两个直波导
s1 = c << gf.get_component("straight", length=20, cross_section=section_outer)
s2 = c << gf.get_component("straight", length=20, cross_section=section_outer)

# 连接直波导到耦合器的端口
s1.connect("o1", coupler_ref.ports["o1"])
s2.connect("o1", coupler_ref.ports["o4"])

# 添加端口（新的端口位置）
c.add_port("o1", port=s1.ports["o2"])
c.add_port("o2", port=coupler_ref.ports["o2"])
c.add_port("o3", port=coupler_ref.ports["o3"])
c.add_port("o4", port=s2.ports["o2"])

c.pprint_ports()
c.draw_ports()
c.plot()
c.show()

c = gf.Component()
    # 这里是波导（1，0）
    #WG = gf.components.bends.bend_euler_s(layer=(1,0))
    #WG=bend_s_offset(offset=offset, radius=radius)
    #WG=gf.components.crossing_etched(width=0.8, r1=r1, r2=r2, w=w, L=3.5, layer_wg='WG')
    #xs = gf.cross_section.strip(width=1, layer=(1, 0))
    # WG=gf.components.straight(
    # length=10,  # Length in microns
    # cross_section=xs
    # )       
section_inner = gf.cross_section.cross_section(
width=1.5,
offset=0,
layer=(1, 0)
)
section_outer = gf.cross_section.cross_section(
    width=2,
    offset=0,
    layer=(1, 0)
)

# 创建基础环形耦合器
coupler = gf.components.coupler_ring_bend(
    coupler_gap=0.5,
    radius=50,
    coupling_angle_coverage=20,
    length_x=0,
    cross_section_inner=section_inner,
    cross_section_outer=section_outer,
    bend_output="bend_euler",
    straight="straight",
)

# 创建新的组件
WG = gf.Component()

# 添加耦合器
coupler_ref = WG << coupler

# 添加两个直波导
s1 = WG << gf.get_component("straight", length=20, cross_section=section_outer)
s2 = WG << gf.get_component("straight", length=20, cross_section=section_outer)

# 连接直波导到耦合器的端口
s1.connect("o1", coupler_ref.ports["o1"])
s2.connect("o1", coupler_ref.ports["o4"])

# 添加端口（新的端口位置）
WG.add_port("o1", port=s1.ports["o2"])
# WG.add_port("o2", port=coupler_ref.ports["o2"])

WG.add_port("o3", port=coupler_ref.ports["o3"])
# WG.add_port("o4", port=s2.ports["o2"])
# Add the MMI component as a reference to our main component `c`.
WG_ref = c.add_ref(WG)
# Create a rectangle background with the same size as the MMI.
# The .size attribute is a convenient way to get the (width, height) tuple.
box = gf.components.rectangle(
    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(3,0)
)
slab= gf.components.rectangle(
    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(2,0)
)
clad=gf.components.rectangle(
    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(5,0)
)
# Add the background rectangle as a reference to `c`.
rect_ref = c.add_ref(box)
slab_ref = c.add_ref(slab) 
clad_ref = c.add_ref(clad)
# Align the center of the rectangle with the center of the MMI.
WG_ref.center = (0, 0)
rect_ref.center = WG_ref.center
slab_ref.center = WG_ref.center
clad_ref.center = WG_ref.center
# Add the optical ports from the MMI reference to the main component `c`.
c.add_ports(WG_ref.ports)
c.plot()
c.draw_ports()

import gdsfactory as gf
# define a custom cross-section
custom_in = gf.cross_section.cross_section(
    width=1.5,
    offset=0,
    layer=(1, 0)
)
custom_out = gf.cross_section.cross_section(
    width=2,
    offset=0,
    layer=(1, 0)
)
c = gf.components.ring_single_bend_coupler(
    radius=50,
    gap=2,
    coupling_angle_coverage=120,
    bend="bend_circular",
    bend_output="bend_euler",
    length_x=0,
    length_y=0,
    cross_section_inner=custom_in,
    cross_section_outer=custom_out
).copy()
c.draw_ports()
c.plot()

import gdsfactory as gf
c = gf.Component()
custom_in = gf.cross_section.cross_section(
    width=1.5,
    offset=0,
    layer=(1, 0)
)
cp = gf.component.coupler_bend(
        radius=10,
        coupler_gap=2,
        coupling_angle_coverage=180,
        cross_section_inner=custom_in,
        cross_section_outer=custom_in,
       
    )
cp.show()
sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)
sout = gf.get_component(
        straight, length=length_x, cross_section=cross_section_outer
    )

coupler_right = c << cp
coupler_left = c << cp
straight_inner = c << sin
straight_inner.movex(-length_x / 2)
straight_outer = c << sout
straight_outer.movex(-length_x / 2)

coupler_left.connect("o1", straight_outer.ports["o1"])
straight_inner.connect("o1", coupler_left.ports["o2"])
coupler_right.connect("o2", straight_inner.ports["o2"], mirror=True)
straight_outer.connect("o2", coupler_right.ports["o1"])

c.add_port("o1", port=coupler_left.ports["o3"])
c.add_port("o2", port=coupler_left.ports["o4"])
c.add_port("o4", port=coupler_right.ports["o3"])
c.add_port("o3", port=coupler_right.ports["o4"])
    # c.flatten()
  

@gf.cell_with_module_name
def coupler_ring_bend(
    radius: float | None = None,
    coupler_gap: float = 0.2,
    coupling_angle_coverage: float = 90.0,
    length_x: float = 0.0,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    bend: AnyComponentFactory = bend_circular_all_angle,
    bend_output: ComponentSpec = "bend_euler",
    straight: ComponentSpec = "straight",
) -> Component:
    r"""Two back-to-back coupler_bend.

    Args:
        radius: um. Default is None, which uses the default radius of the cross_section.
        coupler_gap: um.
        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        coupling_angle_coverage: degrees.
        length_x: horizontal straight length.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        bend: for bend.
        bend_output: for bend.
        straight: for straight.
    """
    c = Component()
    cp = coupler_bend(
        radius=radius,
        coupler_gap=coupler_gap,
        coupling_angle_coverage=coupling_angle_coverage,
        cross_section_inner=cross_section_inner,
        cross_section_outer=cross_section_outer,
        bend=bend,
        bend_output=bend_output,
    )
    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)
    sout = gf.get_component(
        straight, length=length_x, cross_section=cross_section_outer
    )

    coupler_right = c << cp
    coupler_left = c << cp
    straight_inner = c << sin
    straight_inner.movex(-length_x / 2)
    straight_outer = c << sout
    straight_outer.movex(-length_x / 2)

    coupler_left.connect("o1", straight_outer.ports["o1"])
    straight_inner.connect("o1", coupler_left.ports["o2"])
    coupler_right.connect("o2", straight_inner.ports["o2"], mirror=True)
    straight_outer.connect("o2", coupler_right.ports["o1"])

    c.add_port("o1", port=coupler_left.ports["o3"])
    c.add_port("o2", port=coupler_left.ports["o4"])
    c.add_port("o4", port=coupler_right.ports["o3"])
    c.add_port("o3", port=coupler_right.ports["o4"])
    # c.flatten()
    return c

import gdsfactory as gf

c = gf.components.coupler_bent(gap=0.2, radius=26, length=5, width1=0.4, width2=0.4, length_straight=0, cross_section='strip').copy()
c.draw_ports()
c.plot()

import numpy as np

import gdsfactory as gf


@gf.cell_with_module_name
def coupler_bent_half(
    gap: float = 0.200,
    radius: float = 26,
    length: float = 0,
    width1: float = 0.400,
    width2: float = 0.400,
    length_straight: float = 0,
    length_straight_exit: float = 0,
    cross_section: str = "strip",
) -> gf.Component:
    """Returns Broadband SOI curved / straight directional coupler.

    Args:
        gap: gap.
        radius: radius coupling.
        length: coupler_length.
        width1: width1.
        width2: width2.
        length_straight: input and output straight length.
        length_straight_exit: length straight exit.
        cross_section: cross_section.
    """
    radius_outer = radius + (width1 + gap) / 2
    radius_inner = radius - (width2 + gap) / 2
    alpha = round(np.rad2deg(length / (2 * radius)), 4)
    beta = alpha

    c = gf.Component()

    xs = gf.get_cross_section(cross_section)
    xs1 = xs.copy(radius=radius_outer, width=width1)
    xs2 = xs.copy(radius=radius_inner, width=width2)

    outer_bend = gf.path.arc(angle=-alpha, radius=radius_outer)
    inner_bend = gf.path.arc(angle=-alpha, radius=radius_inner)

    outer_straight = gf.path.straight(length=length, npoints=100)
    inner_straight = gf.path.straight(length=length, npoints=100)

    outer_exit_bend = gf.path.arc(angle=alpha, radius=radius_outer)
    inner_exit_bend_down = gf.path.arc(angle=-beta, radius=radius_inner)
    inner_exit_bend_up = gf.path.arc(angle=alpha + beta, radius=radius_inner)

    inner_exit_straight = gf.path.straight(
        length=length_straight,
        npoints=100,
    )
    outer_exit_straight = gf.path.straight(
        length=length_straight_exit,
        npoints=100,
    )

    outer = outer_bend + outer_straight + outer_exit_bend + outer_exit_straight
    inner = (
        inner_bend
        + inner_straight
        + inner_exit_bend_down
        + inner_exit_bend_up
        + inner_exit_straight
    )

    inner_component = c << inner.extrude(xs2)
    outer_component = c << outer.extrude(xs1)
    outer_component.movey(+(width1 + gap) / 2)
    inner_component.movey(-(width2 + gap) / 2)

    c.add_port("o1", port=outer_component.ports["o1"])
    c.add_port("o2", port=inner_component.ports["o1"])
    c.add_port("o3", port=outer_component.ports["o2"])
    c.add_port("o4", port=inner_component.ports["o2"])
    c.flatten()
    return c


@gf.cell_with_module_name
def coupler_bent(
    gap: float = 0.200,
    radius: float = 26,
    length: float = 8.6,
    width1: float = 0.400,
    width2: float = 0.400,
    length_straight: float = 10,
    cross_section: str = "strip",
) -> gf.Component:
    """Returns Broadband SOI curved / straight directional coupler.

    based on: https://doi.org/10.1038/s41598-017-07618-6.

    Args:
        gap: gap.
        radius: radius coupling.
        length: coupler_length.
        width1: width1.
        width2: width2.
        length_straight: input and output straight length.
        cross_section: cross_section.
    """
    c = gf.Component()

    right_half = c << coupler_bent_half(
        gap=gap,
        radius=radius,
        length=length,
        width1=width1,
        width2=width2,
        length_straight=length_straight,
        cross_section=cross_section,
    )
    left_half = c << coupler_bent_half(
        gap=gap,
        radius=radius,
        length=length,
        width1=width1,
        width2=width2,
        length_straight=length_straight,
        cross_section=cross_section,
    )

    left_half.connect(port="o1", other=right_half.ports["o1"], mirror=True)

    c.add_port("o1", port=left_half.ports["o3"])
    c.add_port("o2", port=left_half.ports["o4"])
    c.add_port("o3", port=right_half.ports["o3"])
    c.add_port("o4", port=right_half.ports["o4"])

    c.flatten()
    return c


if __name__ == "__main__":
    c = coupler_bent_half()
    # c = coupler_bent_half()
    # c = coupler_bent()
    c.plot()


from __future__ import annotations

import gdsfactory as gf
from gdsfactory.component import Component
from gdsfactory.components.couplers.coupler import coupler_straight
from gdsfactory.components.couplers.coupler90 import coupler90
from gdsfactory.typings import ComponentSpec, CrossSectionSpec


@gf.cell_with_module_name
def coupler_ring(
    gap: float = 0.2,
    radius: float = 5.0,
    length_x: float = 4.0,
    bend: ComponentSpec = "bend_euler",
    straight: ComponentSpec = "straight",
    cross_section: CrossSectionSpec = "strip",
    cross_section_bend: CrossSectionSpec | None = None,
    length_extension: float = 3.0,
) -> Component:
    r"""Coupler for ring.

    Args:
        gap: spacing between parallel coupled straight waveguides.
        radius: of the bends.
        length_x: length of the parallel coupled straight waveguides.
        bend: 90 degrees bend spec.
        straight: straight spec.
        cross_section: cross_section spec.
        cross_section_bend: optional bend cross_section spec.
        length_extension: straight length extension at the end of the coupler bottom ports.

    .. code::

          o2            o3
           |             |
            \           /
             \         /
           ---=========---
        o1    length_x   o4

          o2                              o3
          xx                              xx
          xx                             xx
           xx          length_x          x
            xx     ◄───────────────►    x
             xx                       xxx
               xx                   xxx
                xxx──────▲─────────xxx
                         │gap
                 o1──────▼─────────◄──────────────► o4
                                    length_extension
    """
    if length_extension is None:
        length_extension = 3 + radius

    c = Component()
    gap = gf.snap.snap_to_grid(gap, grid_factor=2)
    cross_section_bend = cross_section_bend or cross_section

    # define subcells
    coupler90_component = gf.get_component(
        coupler90,
        gap=gap,
        radius=radius,
        bend=bend,
        straight=straight,
        cross_section=cross_section,
        cross_section_bend=cross_section_bend,
        length_straight=length_extension,
    )
    coupler_straight_component = gf.get_component(
        coupler_straight,
        gap=gap,
        length=length_x,
        cross_section=cross_section,
    )

    # add references to subcells
    cbl = c << coupler90_component
    cbr = c << coupler90_component
    cs = c << coupler_straight_component

    # connect references
    cs.connect(port="o4", other=cbr.ports["o1"])
    cbl.connect(port="o2", other=cs.ports["o2"], mirror=True)

    c.add_port("o1", port=cbl.ports["o4"])
    c.add_port("o2", port=cbl.ports["o3"])
    c.add_port("o3", port=cbr.ports["o3"])
    c.add_port("o4", port=cbr.ports["o4"])

    c.add_ports(
        gf.port.select_ports_list(ports=cbl.ports, port_type="electrical"), prefix="cbl"
    )
    c.add_ports(
        gf.port.select_ports_list(ports=cbr.ports, port_type="electrical"), prefix="cbr"
    )
    c.auto_rename_ports()
    c.flatten()
    return c


if __name__ == "__main__":
    c = coupler_ring()
    c.plot()


from __future__ import annotations

from typing import Any

import gdsfactory as gf
from gdsfactory.component import Component
from gdsfactory.components.bends.bend_circular import bend_circular_all_angle
from gdsfactory.typings import AnyComponentFactory, ComponentSpec, CrossSectionSpec


@gf.cell_with_module_name
def coupler_bend(
    radius: float = 10.0,
    coupler_gap: float = 0.2,
    coupling_angle_coverage: float = 120.0,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    bend: AnyComponentFactory = bend_circular_all_angle,
    bend_output: ComponentSpec = "bend_euler",
) -> Component:
    r"""Compact curved coupler with bezier escape.

    TODO: fix for euler bends.

    Args:
        radius: um.
        coupler_gap: um.
        coupling_angle_coverage: degrees.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        bend: for bend.
        bend_output: for bend.

    .. code::

            r   4
            |   |
            |  / ___3
            | / /
        2____/ /
        1_____/
    """
    c = Component()

    xi = gf.get_cross_section(cross_section_inner)
    xo = gf.get_cross_section(cross_section_outer)

    angle_inner = 90
    angle_outer = coupling_angle_coverage / 2
    gap = coupler_gap

    width = xo.width / 2 + xi.width / 2
    spacing = gap + width

    bend90_inner_right = gf.get_component(
        bend,  # type: ignore[arg-type]
        radius=radius,
        cross_section=cross_section_inner,
        angle=angle_inner,
    )
    bend_output_right = gf.get_component(
        bend,  # type: ignore[arg-type]
        radius=radius + spacing,
        cross_section=cross_section_outer,
        angle=angle_outer,
    )
    bend_inner_ref = c.create_vinst(bend90_inner_right)
    bend_output_ref = c.create_vinst(bend_output_right)

    output = gf.get_component(
        bend_output, angle=angle_outer, cross_section=cross_section_outer
    )
    output_ref = c.create_vinst(output)
    output_ref.connect("o1", bend_output_ref.ports["o2"], mirror=True)

    pbw = bend_inner_ref.ports["o1"]
    bend_inner_ref.movey(pbw.center[1] + spacing)

    c.add_port("o1", port=bend_output_ref.ports["o1"])
    c.add_port("o2", port=bend_inner_ref.ports["o1"])
    c.add_port("o3", port=output_ref.ports["o2"])
    c.add_port("o4", port=bend_inner_ref.ports["o2"])
    return c


@gf.cell_with_module_name
def coupler_ring_bend(
    radius: float = 10.0,
    coupler_gap: float = 0.2,
    coupling_angle_coverage: float = 90.0,
    length_x: float = 0.0,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    bend: AnyComponentFactory = bend_circular_all_angle,
    bend_output: ComponentSpec = "bend_euler",
    straight: ComponentSpec = "straight",
) -> Component:
    r"""Two back-to-back coupler_bend.

    Args:
        radius: um.
        coupler_gap: um.
        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        coupling_angle_coverage: degrees.
        length_x: horizontal straight length.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        bend: for bend.
        bend_output: for bend.
        straight: for straight.
    """
    c = Component()
    cp = coupler_bend(
        radius=radius,
        coupler_gap=coupler_gap,
        coupling_angle_coverage=coupling_angle_coverage,
        cross_section_inner=cross_section_inner,
        cross_section_outer=cross_section_outer,
        bend=bend,
        bend_output=bend_output,
    )
    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)
    sout = gf.get_component(
        straight, length=length_x, cross_section=cross_section_outer
    )

    coupler_right = c << cp
    coupler_left = c << cp
    straight_inner = c << sin
    straight_inner.movex(-length_x / 2)
    straight_outer = c << sout
    straight_outer.movex(-length_x / 2)

    coupler_left.connect("o1", straight_outer.ports["o1"])
    straight_inner.connect("o1", coupler_left.ports["o2"])
    coupler_right.connect("o2", straight_inner.ports["o2"], mirror=True)
    straight_outer.connect("o2", coupler_right.ports["o1"])

    c.add_port("o1", port=coupler_left.ports["o3"])
    c.add_port("o2", port=coupler_left.ports["o4"])
    c.add_port("o4", port=coupler_right.ports["o3"])
    c.add_port("o3", port=coupler_right.ports["o4"])
    # c.flatten()
    return c


@gf.cell_with_module_name
def ring_single_bend_coupler(
    radius: float = 5.0,
    gap: float = 0.2,
    coupling_angle_coverage: float = 90.0,
    bend_all_angle: AnyComponentFactory = bend_circular_all_angle,
    bend: ComponentSpec = "bend_circular",
    bend_output: ComponentSpec = "bend_euler",
    length_x: float = 0.6,
    length_y: float = 0.6,
    cross_section_inner: CrossSectionSpec = "strip",
    cross_section_outer: CrossSectionSpec = "strip",
    **kwargs: Any,
) -> Component:
    r"""Returns ring with curved coupler.

    TODO: enable euler bends.

    Args:
        radius: um.
        gap: um.
        coupling_angle_coverage: degrees.
        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.
        bend_all_angle: for bend.
        bend: for bend.
        bend_output: for bend.
        length_x: horizontal straight length.
        length_y: vertical straight length.
        cross_section_inner: spec inner bend.
        cross_section_outer: spec outer bend.
        kwargs: cross_section settings.
    """
    c = Component()

    coupler = coupler_ring_bend(
        radius=radius,
        coupler_gap=gap,
        coupling_angle_coverage=coupling_angle_coverage,
        length_x=length_x,
        cross_section_inner=cross_section_inner,
        cross_section_outer=cross_section_outer,
        bend=bend_all_angle,
        bend_output=bend_output,
    )
    cb = c << coupler

    cross_section = cross_section_inner
    straight = gf.c.straight
    sx = gf.get_component(
        straight, length=length_x, cross_section=cross_section, **kwargs
    )
    sy = gf.get_component(
        straight, length=length_y, cross_section=cross_section, **kwargs
    )
    b = gf.get_component(bend, cross_section=cross_section, radius=radius, **kwargs)
    sl = c << sy
    sr = c << sy
    bl = c << b
    br = c << b
    st = c << sx

    sl.connect(port="o1", other=cb["o2"])
    bl.connect(port="o2", other=sl["o2"], mirror=True)
    st.connect(port="o2", other=bl["o1"])
    sr.connect(port="o1", other=br["o1"])
    sr.connect(port="o2", other=cb["o3"])
    br.connect(port="o2", other=st["o1"], mirror=True)

    c.add_port("o2", port=cb["o4"])
    c.add_port("o1", port=cb["o1"])
    c.flatten()
    return c


if __name__ == "__main__":
    # c = coupler_bend()
    # n = c.get_netlist()
    c = ring_single_bend_coupler()
    # c = ring_single_bend_coupler()
    c.pprint_ports()
    c.plot()


import numpy as np
import gdsfactory as gf
import matplotlib.pyplot as plt

def gvc_bend(
    final_angle: float = 45.0,
    final_curvature: float = -0.2,
    peak_curvature: float = 0.3,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """General Variable Curvature Bend.

    Args:
        final_angle: Final angle in degrees.
        final_curvature: Final curvature in 1/μm.
        peak_curvature: Peak curvature in 1/μm.
        cross_section: Cross-section spec.
        npoints: Number of points for discretization.

    """
    if peak_curvature <= 0 or final_curvature >= 0:
        raise ValueError("For this example, assume positive peak_curvature and negative final_curvature.")
    if peak_curvature + final_curvature <= 0:
        raise ValueError("peak_curvature must be > -final_curvature.")

    theta = np.deg2rad(final_angle)
    k_final = final_curvature
    k_peak = peak_curvature

    r = -k_final / k_peak + 1
    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)
    sp2 = r * sp1

    # Discretize
    ds1 = sp1 / (npoints // 2 - 1) if sp1 > 0 else 0
    s1 = np.linspace(0, sp1, npoints // 2)
    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.array([])
    theta_cum1 = np.cumsum(k1) * ds1

    ds2 = sp2 / (npoints // 2 - 1) if sp2 > 0 else 0
    s2 = np.linspace(0, sp2, npoints // 2)
    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.array([])
    theta_cum2 = np.cumsum(k2) * ds2 + (theta_cum1[-1] if len(theta_cum1) > 0 else 0)

    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp2 > 0 else theta_cum1

    ds = np.concatenate((np.full(len(theta_cum1) - 1, ds1), np.full(len(theta_cum2), ds2))) if sp2 > 0 else np.full(len(theta_cum), ds1)

    # Positions using trapezoid rule approximation, but for simplicity use cumsum * ds assuming dense points
    x = np.cumsum(np.cos(theta_cum) * ds)
    y = np.cumsum(np.sin(theta_cum) * ds)
    points = np.column_stack((x, y))

    # Create the path using gdsfactory Path
    P = gf.Path()
    P.append(points)
    
    # Create the cross-section
    if isinstance(cross_section, str):
        # Use default cross-section if string is provided
        cs = gf.get_cross_section(cross_section)
    else:
        cs = cross_section
    
    # Extrude the path to create the component
    c = gf.path.extrude(P, cs)

    # Add ports at the beginning and end
    # if len(points) > 0:
    #     # Start port
    #     c.add_port(name="o1", center=(0, 0), orientation=0, cross_section=cs)
        
    #     # End port
    #     final_center = points[-1]
    #     final_orientation = np.rad2deg(theta_cum[-1])
    #     c.add_port(name="o2", center=final_center, orientation=final_orientation, cross_section=cs)

    return c

# Example usage
c = gvc_bend()
c.draw_ports()
c.pprint_ports()
c.plot()
c.show()

import gdsfactory as gf
import numpy as np
@gf.cell
def gvc_bend_optimal(
    final_angle: float = 45.0,
    final_curvature: float = -0.1,
    peak_curvature: float = 0.2,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """
    创建一个理论最优的通用可变曲率弯曲 (C2连续)。

    该弯曲的曲率 k(s) 及其一阶导数 dk/ds 全程连续，
    从而最大限度地减少过渡损耗。

    Args:
        final_angle: 最终的目标角度 (degrees)。
        final_curvature: 最终的目标曲率 (1/μm)。正=逆时针, 负=顺时针。
        peak_curvature: 路径中的峰值曲率 (1/μm)。这是一个正值，
                        用作损耗/尺寸的控制参数。值越小，弯曲越平缓，
                        损耗越低，但尺寸越大。
        cross_section: 波导的横截面。
        npoints: 离散化点数。
    """
    # --- 输入参数验证 ---
    k_final = final_curvature
    k_peak = peak_curvature
    
    if k_peak <= 0:
        raise ValueError("peak_curvature 必须为正数。")
    if np.abs(k_final) >= k_peak:
        raise ValueError(
            f"peak_curvature ({k_peak}) 必须大于等于最终曲率的绝对值 "
            f"abs(final_curvature) ({np.abs(k_final)})."
        )

    # --- 核心逻辑: 根据输入参数反解路径长度 L1 和 L2 ---
    # 我们建立一个模型，其中曲率由两段升余弦函数构成，
    # 并假设两段的最大变化率 dk/ds 大小相等，以获得对称平滑的过渡。
    # 最终可以推导出 L1 和 L2 的解析解：
    
    theta_final_rad = np.deg2rad(final_angle)
    
    # 求解 L1 (从 k=0 到 k=k_peak 的路径长度)
    denominator = (k_peak / 2) + (k_peak + k_final) / 2 * (k_peak - k_final) / k_peak
    if np.isclose(denominator, 0):
        # 避免除以零
        denominator = 1e-9
    L1 = theta_final_rad / denominator

    # 求解 L2 (从 k=k_peak 到 k=k_final 的路径长度)
    L2 = L1 * (k_peak - k_final) / k_peak
    
    if L1 < 0 or L2 < 0:
        raise ValueError(
            "计算出的路径长度为负，无法创建弯曲。 "
            "请尝试增大 'peak_curvature' 或减小 'final_angle'。"
        )

    # --- 构建路径 (修正后的逻辑) ---
    # 1. 创建总路径长度数组
    s_total = np.linspace(0, L1 + L2, npoints)
    k = np.zeros_like(s_total)

    # 2. 根据s_total中的每个点，计算对应的曲率k
    for i, s in enumerate(s_total):
        if s <= L1:
            # 第一部分: 曲率从 0 上升到 k_peak
            k[i] = (k_peak / 2) * (1 - np.cos(np.pi * s / L1))
        else:
            # 第二部分: 曲率从 k_peak 变化到 k_final
            s_prime = s - L1
            k[i] = (k_peak + k_final) / 2 + (k_peak - k_final) / 2 * np.cos(np.pi * s_prime / L2)

    # 3. 通过积分计算角度和位置 (使用梯形法则以提高精度)
    theta_cum = np.zeros_like(s_total)
    for i in range(1, npoints):
        theta_cum[i] = theta_cum[i-1] + (k[i] + k[i-1]) * (s_total[i] - s_total[i-1]) / 2

    x = np.zeros_like(s_total)
    y = np.zeros_like(s_total)
    for i in range(1, npoints):
        ds = s_total[i] - s_total[i-1]
        avg_angle = (theta_cum[i] + theta_cum[i-1]) / 2
        x[i] = x[i-1] + np.cos(avg_angle) * ds
        y[i] = y[i-1] + np.sin(avg_angle) * ds
        
    points = np.column_stack((x, y))

    # --- 创建 gdsfactory 组件 ---
    c = gf.Component()
    path = gf.Path(points)
    bend = c << gf.path.extrude(path, cross_section=cross_section)
    c.add_ports(bend.ports)
    
    # 打印一些计算出的参数作为参考
    print(
        f"输入: angle={final_angle}, k_final={k_final}, k_peak={k_peak} -> "
        f"计算得出: L1={L1:.2f}um, L2={L2:.2f}um, "
        f"最终角度={np.rad2deg(theta_cum[-1]):.2f}deg"
    )

    return c

if __name__ == "__main__":
    # --- 示例：比较不同 peak_curvature 的效果 ---
    # 我们的目标是：最终角度45度，最终曲率为-0.1 (半径-10um的顺时针弯曲)

    # 示例1：使用较高的 peak_curvature (更紧凑，更高损耗)
    c1 = gvc_bend_optimal(
        final_angle=45, final_curvature=-1/100, peak_curvature=1.05/100
    )

    # 示例2：使用较低的 peak_curvature (更舒展，更低损耗)
    c2 = gvc_bend_optimal(
        final_angle=45, final_curvature=-1/100, peak_curvature=1.05/100
    )
    
    # 将它们放在一起进行比较
    c = gf.Component()
    # ref1 = c_compare << c1
    ref2 = c << c2
    
    # 为了方便比较，将第二个弯曲的起点对齐到第一个的终点
    # ref2.connect("o1", ref1.ports["o2"])
    c2.draw_ports()
    c2.plot()
    c2.show()


def looploop(num_pts=1000):
    """Simple limacon looping curve."""
    t = np.linspace(-np.pi, 0, num_pts)
    r = 20 + 25 * np.sin(t)
    x = r * np.cos(t)
    y = r * np.sin(t)
    return np.array((x, y)).T


# Create the path points
P = gf.Path()
P.append(gf.path.arc(radius=10, angle=90))
P.append(gf.path.straight())
P.append(gf.path.arc(radius=5, angle=-90))
P.append(looploop(num_pts=1000))
P.rotate(-45)

# Create the crosssection
s0 = gf.Section(width=1, offset=0, layer=(1, 0), port_names=("in", "out"))
s1 = gf.Section(width=0.5, offset=2, layer=(2, 0))
s2 = gf.Section(width=0.5, offset=4, layer=(3, 0))
s3 = gf.Section(width=1, offset=0, layer=(4, 0))
X = gf.CrossSection(sections=(s0, s1, s2, s3))

c = gf.path.extrude(P, X)
c.plot()

import numpy as np
import gdsfactory as gf

@gf.cell
def gvc_bend(
    final_angle: float = 45.0,
    final_curvature: float = -0.1,
    peak_curvature: float = 0.2,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """General Variable Curvature Bend.

    Args:
        final_angle: Final angle in degrees.
        final_curvature: Final curvature in 1/μm.
        peak_curvature: Peak curvature in 1/μm.
        cross_section: Cross-section spec.
        npoints: Number of points for discretization.

    """
    if peak_curvature <= 0 or final_curvature >= 0:
        raise ValueError("For this example, assume positive peak_curvature and negative final_curvature.")
    if peak_curvature + final_curvature <= 0:
        raise ValueError("peak_curvature must be > -final_curvature.")

    theta = np.deg2rad(final_angle)
    k_final = final_curvature
    k_peak = peak_curvature

    r = -k_final / k_peak + 1
    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)
    sp2 = r * sp1

    # Discretize with trapezoid integration for accuracy
    n1 = npoints // 2
    n2 = npoints - n1  # Ensure total npoints

    s1 = np.linspace(0, sp1, n1)
    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.zeros(n1)
    theta_cum1 = np.cumtrapz(k1, s1, initial=0)  # Cumtrapz for precise cumulative integral

    s2 = np.linspace(0, sp2, n2)
    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.zeros(n2)
    theta_cum2 = np.cumtrapz(k2, s2, initial=0) + theta_cum1[-1]

    s = np.concatenate((s1[:-1], s1[-1] + s2)) if sp1 > 0 and sp2 > 0 else (s1 if sp1 > 0 else s2)
    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp1 > 0 and sp2 > 0 else (theta_cum1 if sp1 > 0 else theta_cum2)

    # ds for position integration (approximate with diff(s))
    ds = np.diff(s, prepend=0)  # ds[0]=0, but positions start from 0

    # Positions: integrate cos/sin with trapezoid for better accuracy
    cos_theta = np.cos(theta_cum)
    sin_theta = np.sin(theta_cum)
    x = np.cumtrapz(cos_theta, s, initial=0)
    y = np.cumtrapz(sin_theta, s, initial=0)

    points = np.column_stack((x, y))

    p = gf.Path()
    p.points = points

    c = p.extrude(cross_section=cross_section)

    # Add ports
    c.add_port(name="o1", center=(0, 0), orientation=0, cross_section=cross_section)

    final_center = points[-1]
    final_orientation = np.rad2deg(theta_cum[-1])
    c.add_port(name="o2", center=final_center, orientation=final_orientation, cross_section=cross_section)

    return c

# Example usage
c = gvc_bend()
c.show()

cs = gf.cross_section.strip(width=1, layer=(1, 0), radius_min=100.0)
c= gf.components.bend_s(size=(400, 300),
                             npoints=200, cross_section=cs, allow_min_radius_violation=False)
c.draw_ports()
c.plot()