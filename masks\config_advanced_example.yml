# 🐱 高级配置示例 - 展示两种参数扫描方式

# ===== 芯片基本信息 =====
chip_settings:
  name: "advanced_parameter_sweep"
  size: [15000.0, 15000.0]
  spacing: [0, 500]

# ===== PCells库定义 =====
pcells:
  ring_heater:
    component: "ring_with_electrode_with_heater_with_tip"
    defaults:
      ring_width: 1.5
      waveguide_width: 1
      gap: 0.6
      coupling_angle_coverage: 10
      ring_radius: 99.25
      heater_width: 3
      length_left: 100
      length_total: 600
      
  simple_electrode:
    component: "waveguide_tip_with_ppln_electrodes"
    defaults:
      wg_width: 2
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      bend_radius: 100
      offset: -300
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
      
  thermal_ring:
    component: "thermal_heater_ring"
    defaults:
      ring_radius: 50.0
      gap: 0.2
      ring_width: 0.5
      heater_width: 2.0

# ===== 布局设置 =====
layout:
  grid:
    rows: 4                    # 4行展示不同配置
    cols: 2                    # 2列展示不同方式
    row_spacing: 400
    col_spacing: 1000
    
  labels:
    enabled: true
    format: "{col_letter}{row_number}"
    default_offset: 50
    
  internal:
    default_spacing: -300
    default_count: 3          # 每个smallpart默认3个组件
    
  # 列定义
  columns:
    # ========== 第1列：同一个smallpart内参数扫描 ==========
    col_0:
      pcell_type: ring_heater
      description: "同一smallpart内length_left扫描"
      base_params: {}
      variations:
        # 方式1：使用parameter_sweep定义同一smallpart内的多个组件
        - parameter_sweep:
            - {length_left: 100}  # 第1个组件
            - {length_left: 200}  # 第2个组件  
            - {length_left: 300}  # 第3个组件
        - parameter_sweep:
            - {length_left: 150, ring_radius: 80}
            - {length_left: 250, ring_radius: 90}
            - {length_left: 350, ring_radius: 100}
        - parameter_sweep:
            - {gap: 0.4, heater_width: 2}
            - {gap: 0.6, heater_width: 3}
            - {gap: 0.8, heater_width: 4}
        - parameter_sweep:
            - {coupling_angle_coverage: 5}
            - {coupling_angle_coverage: 10}
            - {coupling_angle_coverage: 15}
      position_offset: [0, 0]
      
    # ========== 第2列：同一列不同PCells ==========
    col_1:
      # 注意：使用multi_pcells来定义不同的组件类型
      multi_pcells: true
      description: "同一列不同PCells"
      variations:
        # 每行使用不同的pcell_type
        - pcell_type: ring_heater
          params: {length_left: 100, ring_radius: 50}
        - pcell_type: simple_electrode  
          params: {wg_width: 1.5, poling_length: 800}
        - pcell_type: thermal_ring
          params: {ring_radius: 30, gap: 0.15}
        - pcell_type: ring_heater
          params: {length_left: 400, heater_width: 5}
      position_offset: [0, 0]

# ===== 标记设置 =====
marks:
  alignment:
    enabled: true
    layer: "MARK"
    margins: [1500, 1000, 1000, 1500]
    spacing: 1000
    cross_size: [50, 2]
    
  dicing:
    smallpart_enabled: true
    chip_enabled: true
    layer: "DICING"
    mark_size: [50, 20]
    boundary_offset: [-200, 200]

# ===== 输出设置 =====
output:
  path: "advanced_sweep.gds"
  show_gds: true
