"""检查自定义PDK中的taper组件"""

import gdsfactory as gf
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

print("=== 检查自定义PDK中的taper组件 ===")

# 导入自定义PDK
from pdk import PDK, LAYERS

# 获取当前PDK
current_pdk = gf.get_active_pdk()
print(f"当前PDK: {current_pdk.name}")

# 检查cells中的taper相关组件
print(f"\n自定义PDK中的组件总数: {len(current_pdk.cells)}")
print("\n自定义PDK中的taper相关组件:")
taper_components = []
for name, component in current_pdk.cells.items():
    if 'taper' in name.lower():
        taper_components.append(name)
        print(f"  - {name}")

if not taper_components:
    print("  没有找到taper相关组件")

# 检查是否可以访问taper_electrical
print("\n检查taper_electrical组件:")
try:
    taper_electrical = gf.get_component("taper_electrical")
    print(f"  taper_electrical存在: {taper_electrical.name}")
    print(f"  端口数量: {len(taper_electrical.ports)}")
    for port_name, port in taper_electrical.ports.items():
        print(f"    {port_name}: width={port.width}, layer={port.layer}, type={port.port_type}")
except Exception as e:
    print(f"  taper_electrical不存在: {e}")

# 检查是否可以直接创建taper
print("\n检查直接创建taper:")
try:
    taper = gf.components.taper_electrical(
        width1=5.0,
        width2=10.0,
        length=10.0,
        layer=(49, 0)  # HEATER layer
    )
    print(f"  electrical taper创建成功: {taper.name}")
    print(f"  端口数量: {len(taper.ports)}")
    for port_name, port in taper.ports.items():
        print(f"    {port_name}: width={port.width}, layer={port.layer}, type={port.port_type}")
except Exception as e:
    print(f"  electrical taper创建失败: {e}")

print("\n=== 检查完成 ===")
