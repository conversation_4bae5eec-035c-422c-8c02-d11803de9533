import gdsfactory as gf
import numpy as np
from gdsfactory.typings import LayerSpec
import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from pdk.layers import LAYER


def validate_parameters(
    poling_length: float,
    period: float,
    duty_cycle: float,
    finger_length: float,
    bus_width: float,
    gap_to_wg: float,
    fillet_radius: float,
    bus_padding: float,
) -> None:
    """Validate PPLN electrode parameters.
    
    Args:
        poling_length: Total length of poling region
        period: QPM period
        duty_cycle: Metal finger width to period ratio
        finger_length: Length of electrode fingers
        bus_width: Width of bus bar
        gap_to_wg: Distance from finger tips to waveguide center
        fillet_radius: Corner rounding radius
        bus_padding: Extra length on each side of the bus bar
    
    Raises:
        ValueError: When parameters are invalid
    """
    if poling_length <= 0:
        raise ValueError(f"Poling length must be positive, got: {poling_length}")
    if period <= 0:
        raise ValueError(f"Period must be positive, got: {period}")
    if not 0 < duty_cycle < 1:
        raise ValueError(f"Duty cycle must be between 0 and 1, got: {duty_cycle}")
    if finger_length <= 0:
        raise ValueError(f"Finger length must be positive, got: {finger_length}")
    if bus_width <= 0:
        raise ValueError(f"Bus width must be positive, got: {bus_width}")
    if gap_to_wg < 0:
        raise ValueError(f"Gap to waveguide cannot be negative, got: {gap_to_wg}")
    if fillet_radius < 0:
        raise ValueError(f"Fillet radius cannot be negative, got: {fillet_radius}")
    if bus_padding < 0:
        raise ValueError(f"Bus padding cannot be negative, got: {bus_padding}")
    
    # Check geometric constraints
    if fillet_radius > bus_width / 2:
        raise ValueError(f"Fillet radius cannot be larger than half bus width, got: {fillet_radius} > {bus_width/2}")
    if fillet_radius > period * duty_cycle / 2:
        raise ValueError(f"Fillet radius cannot be larger than half finger width, got: {fillet_radius} > {period*duty_cycle/2}")


def create_electrode_points(
    x_start_bus: float,
    total_length: float,
    y_base: float,
    period: float,
    duty_cycle: float,
    finger_length: float,
    bus_width: float,
    num_periods: int,
    bus_padding: float,
) -> np.ndarray:
    """Create points for electrode polygon.
    
    Args:
        x_start_bus: Starting x coordinate of the bus bar
        total_length: Total length of the bus bar
        y_base: y coordinate of the finger tips
        period: QPM period
        duty_cycle: Metal finger width to period ratio
        finger_length: Length of electrode fingers
        bus_width: Width of bus bar
        num_periods: Number of periods
        bus_padding: Extra length on each side of the bus bar
    
    Returns:
        np.ndarray: Array of polygon points
    """
    points = []
    finger_width = period * duty_cycle
    
    # Calculate x coordinates
    x_end_bus = x_start_bus + total_length
    x_start_fingers = x_start_bus + bus_padding

    # Define y-coordinates
    bus_bottom_y = y_base + finger_length
    bus_top_y = bus_bottom_y + bus_width
    finger_tip_y = y_base

    # Start at top-left corner of the bus bar
    points.append((x_start_bus, bus_top_y))
    # Go to top-right corner
    points.append((x_end_bus, bus_top_y))
    # Go to bottom-right corner
    points.append((x_end_bus, bus_bottom_y))

    # Trace the bottom edge of the electrode, including fingers, from right to left
    for i in range(num_periods - 1, -1, -1):
        finger_x_start = x_start_fingers + i * period
        finger_x_end = finger_x_start + finger_width

        # Bus bar segment right of the finger
        points.append((finger_x_end, bus_bottom_y))
        # Finger right edge (down)
        points.append((finger_x_end, finger_tip_y))
        # Finger tip (horizontal)
        points.append((finger_x_start, finger_tip_y))
        # Finger left edge (up)
        points.append((finger_x_start, bus_bottom_y))

    # Finish at the bottom-left corner of the bus bar
    points.append((x_start_bus, bus_bottom_y))

    return np.array(points)


@gf.cell
def ppln_electrode(
    # Core physical parameters
    poling_length: float = 1000.0,  # Poling length (µm)
    period: float = 40.0,  # Period (µm)
    duty_cycle: float = 0.5,  # Duty cycle (0-1)
    finger_length: float = 50.0,  # Finger length (µm)
    bus_width: float = 50.0,  # Bus bar width (µm)
    gap_to_wg: float = 10.0,  # Gap from finger tips to waveguide center (µm)
    fillet_radius: float = 20.0,  # Fillet radius (µm)
    bus_padding: float | None = None,  # Padding on each side of the bus bar. Defaults to period/2
    port_width: float = 30,  # Port width (µm)
    port_spacing: float = 100,  # Spacing between ports (µm)
    pad_distance: float = 100.0,  # Distance from bus bar to electrical pads (µm)
    pad_width: float = 100.0,  # Width of electrical pads (µm)
    # Other parameters
    layer: LayerSpec = LAYER.METAL,  # Metal layer
) -> gf.Component:
    """Create PPLN periodic poling interdigitated electrodes.
    
    Args:
        poling_length: Total length of poling region
        period: QPM period
        duty_cycle: Metal finger width to period ratio
        finger_length: Length of electrode fingers
        bus_width: Width of bus bar
        gap_to_wg: Distance from finger tips to waveguide center
        fillet_radius: Corner rounding radius
        bus_padding: Extra length on each side of the bus bar
        layer: Metal layer specification
    
    Returns:
        gf.Component: PPLN electrode component with ports:
            - e1_left: Top electrode left electrical port
            - e1_right: Top electrode right electrical port
            - e2_left: Bottom electrode left electrical port
            - e2_right: Bottom electrode right electrical port
    """
    # Set default bus padding if not provided, with robust check for "None" string
    if bus_padding is None or str(bus_padding).lower() == 'none':
        bus_padding = period / 2
    
    # Ensure bus_padding is a number before validation
    bus_padding = float(bus_padding)
        
    # Validate parameters
    validate_parameters(
        poling_length=poling_length,
        period=period,
        duty_cycle=duty_cycle,
        finger_length=finger_length,
        bus_width=bus_width,
        gap_to_wg=gap_to_wg,
        fillet_radius=fillet_radius,
        bus_padding=bus_padding,
    )
    
    c = gf.Component()
    
    # Calculate basic parameters
    num_periods = int(poling_length / period)  # Number of periods
    finger_width = period * duty_cycle
    
    # The actual physical length of the finger pattern
    finger_pattern_length = (num_periods - 1) * period + finger_width if num_periods > 0 else 0
    
    total_length = finger_pattern_length + 2 * bus_padding
    x_start_bus = -total_length / 2
    
    # Create top electrode points
    points_top = create_electrode_points(
        x_start_bus=x_start_bus,
        total_length=total_length,
        y_base=gap_to_wg,  # y-coordinate of finger tips
        period=period,
        duty_cycle=duty_cycle,
        finger_length=finger_length,
        bus_width=bus_width,
        num_periods=num_periods,
        bus_padding=bus_padding,
    )
    
    # Create bottom electrode points (mirror of top)
    points_bottom = np.array([(x, -y) for x, y in points_top])
    points_bottom = points_bottom[::-1]  # Reverse point order for correct drawing direction
    
    # Add polygons to component (will apply rounding at the end)
    c.add_polygon(points_top, layer=layer)
    c.add_polygon(points_bottom, layer=layer)
    
    # Add electrical ports
    # Left ports
    c.add_port(
        name="e1_left",
        center=(-port_spacing, gap_to_wg + finger_length + bus_width),
        width=port_width,
        orientation=90,
        layer=layer,
        port_type="electrical"
    )
    
    c.add_port(
        name="e2_left",
        center=(-port_spacing, -(gap_to_wg + finger_length + bus_width)),
        width=port_width,
        orientation=-90,
        layer=layer,
        port_type="electrical"
    )
    
    # Right ports
    c.add_port(
        name="e1_right",
        center=(port_spacing, gap_to_wg + finger_length + bus_width),
        width=port_width,
        orientation=90,
        layer=layer,
        port_type="electrical"
    )
    
    c.add_port(
        name="e2_right",
        center=(port_spacing, -(gap_to_wg + finger_length + bus_width)),
        width=port_width,
        orientation=-90,
        layer=layer,
        port_type="electrical"
    )
    
    # Add electrical pads (will be rounded with other elements)
    # For e1_left: pad above the port
    e1_left_pad_y = gap_to_wg + finger_length + bus_width + pad_distance
    e1_left_pad = gf.components.pad(size=(pad_width, pad_width), layer=layer)
    e1_left_pad_ref = c.add_ref(e1_left_pad)
    e1_left_pad_ref.center = (-port_spacing, e1_left_pad_y)
    
    # For e2_right: pad below the port
    e2_right_pad_y = -(gap_to_wg + finger_length + bus_width + pad_distance)
    e2_right_pad = gf.components.pad(size=(pad_width, pad_width), layer=layer)
    e2_right_pad_ref = c.add_ref(e2_right_pad)
    e2_right_pad_ref.center = (port_spacing, e2_right_pad_y)
    
    # Apply round corners to ALL elements (electrodes + pads) at once
    if fillet_radius > 0:
        # Create a new component for the rounded version
        c_rounded = gf.Component()
        
        # Convert to database units for round_corners function
        rinner = fillet_radius * 1000  # Convert µm to nm (database units)
        router = fillet_radius * 1000  # Convert µm to nm (database units)  
        n = 64  # Number of points per circle for smooth curves
        
        # Apply rounding to all polygons on the metal layer
        for p in c.get_polygons()[layer]:
            p_round = p.round_corners(rinner, router, n)
            c_rounded.add_polygon(p_round, layer=layer)
        
        # Copy all ports from original component
        for port in c.ports:
            c_rounded.add_port(name=port.name, port=port)
        
        # Now add electrical connections (after rounding, so they stay sharp)
        # Create virtual ports for pad connections since the original pad refs are not available
        e1_left_pad_port = gf.Port(
            name="e1_left_pad",
            center=(-port_spacing, e1_left_pad_y),
            width=port_width,
            orientation=270,
            layer=layer,
            port_type="electrical"
        )
        
        e2_right_pad_port = gf.Port(
            name="e2_right_pad", 
            center=(port_spacing, e2_right_pad_y),
            width=port_width,
            orientation=90,
            layer=layer,
            port_type="electrical"
        )
        
        # Route connection from e1_left port to pad using gdsfactory routing
        route_e1_left = gf.routing.route_single_electrical(
            c_rounded,
            c_rounded.ports["e1_left"],
            e1_left_pad_port,
            start_straight_length=30,
            cross_section="metal_routing",
        )
        
        # Route connection from e2_right port to pad using gdsfactory routing
        route_e2_right = gf.routing.route_single_electrical(
            c_rounded,
            c_rounded.ports["e2_right"],
            e2_right_pad_port,
            start_straight_length=10,
            cross_section="metal_routing",
        )
        
        return c_rounded
    else:
        # No rounding, add connections to original component
        # Route connection from e1_left port to pad using gdsfactory routing
        route_e1_left = gf.routing.route_single_electrical(
            c,
            c.ports["e1_left"],
            e1_left_pad_ref.ports["e4"],
            start_straight_length=30,
            cross_section="metal_routing",
        )
        
        # Route connection from e2_right port to pad using gdsfactory routing
        route_e2_right = gf.routing.route_single_electrical(
            c,
            c.ports["e2_right"],
            e2_right_pad_ref.ports["e2"],
            start_straight_length=10,
            cross_section="metal_routing",
        )
        
        return c


if __name__ == "__main__":
    print("Testing PPLN periodic poling electrode component...")
    
    # Test without rounded corners
    c = ppln_electrode(fillet_radius=0)
    print(f"Component without rounded corners: length={c.xsize:.1f}, width={c.ysize:.1f}")
    
    # Test with rounded corners
    c_round = ppln_electrode(fillet_radius=5)
    print(f"Component with rounded corners: length={c_round.xsize:.1f}, width={c_round.ysize:.1f}")

    # Show the rounded corner component
    c_round.draw_ports()
    c_round.show()
    
