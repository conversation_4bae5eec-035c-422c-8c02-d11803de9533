import gdsfactory as gf
import numpy as np
@gf.cell
def periodic_electrode(
    width: float = 500,              # 电极总宽度 (μm)
    height: float = 2,        # 上矩形高度 (μm)
    gap: float = 10,                # 上下矩形间距 (μm)
    period: float = 5,              # 连接条周期 (μm)
    duty_cycle: float = 0.2,        # 占空比 (0-1)
    layer: tuple = (4, 0)           # 金属层
):
    """
    创建周期性电极结构
    
    Args:
        width: 电极总宽度 (μm)
        height_upper: 上矩形高度 (μm)
        height_lower: 下矩形高度 (μm)
        gap: 上下矩形间距 (μm)
        period: 连接条周期 (μm)
        duty_cycle: 占空比，连接条占周期的比例 (0-1)
        connector_width: 连接条宽度 (μm)
        layer: 金属层定义
    
    Returns:
        gf.Component: 周期性电极组件
    """
    c = gf.Component()
    
    # 1. 创建上矩形
    upper_rect = gf.components.rectangle(
        size=(width, height), 
        layer=layer
    )
    upper_ref = c << upper_rect
    upper_ref.move([0, gap/2 ])
    
    # 2. 创建下矩形
    lower_rect = gf.components.rectangle(
        size=(width, height), 
        layer=layer
    )
    lower_ref = c << lower_rect
    lower_ref.move([0, -gap/2 - height])
    
    # 3. 创建周期性连接条
    connector_length = duty_cycle * period  # 连接条长度
    spacing = period - connector_length     # 连接条间距
    
    # 计算可以放置的连接条数量
    num_connectors = int(width / period)
    x_space=(width-num_connectors*period+spacing)/2
    # 创建每个连接条
    for i in range(num_connectors):
        # 计算连接条的x位置
        x_start =  i * period + x_space
        x_center = x_start + connector_length/2
        connector = gf.components.rectangle(
            size=(connector_length, gap+height/2), 
            layer=layer
        )
        connector_ref = c << connector
        #connector_ref.dmove([x_center, 0])
        connector_ref.center=(x_center,0)
    c.center=(0,0)
    radius=50
    x_pad=20
    distance=20
    y_pad=gap/2+height+distance+radius
    
    # 创建圆形pad
    pad1 = gf.components.circle(radius=radius, layer=layer)
    pad_ref1 = c << pad1
    pad_ref1.center=(0, y_pad)
    points=[(2,gap/2+height),(2,gap/2+height+distance+0.3*radius),(-2,gap/2+height+distance+0.3*radius),(-2,gap/2+height)]
    c.add_polygon(points, layer=layer)
    c.flatten()
    c2=gf.Component()
    rinner = 50  # 	The circle radius of inner corners (in database units).
    router = 50  # 	The circle radius of outer corners (in database units).
    n=2000
    for layer, polygons in c.get_polygons().items():
        for p in polygons:
            p_round = p.round_corners(rinner, router, n)
            c2.add_polygon(p_round, layer=layer)
    return c2
@gf.cell
def waveguide_with_electrode(length: float = 500, 
                            height: float = 2,    
                            wg_width:float=1.5,   
                            gap: float = 10,              
                            period: float = 5,              
                            duty_cycle: float = 0.2,       
                            layer_electrode: tuple = (4, 0), ):
    c = gf.Component()
    electrode_ref = c << periodic_electrode(width=length,           
                                    height=height,    
                                    gap=gap,                
                                    period=period,             
                                    duty_cycle=duty_cycle,        
                                    layer=layer_electrode,)
    wg_section = gf.cross_section.cross_section(
        width=wg_width,
        offset=0,
        layer=(1, 0)
    )
    wg_ref=c<<gf.components.straight(length=length+20, npoints=2, cross_section=wg_section)
    wg_ref.center=(0,0)
    c.add_ports(wg_ref.ports)
    return c





if __name__ == "__main__":
    # 测试代码只在直接运行该文件时执行
    #c = gf.components.extend_ports(component=c, length=50, port_type='optical', centered=False, allow_width_mismatch=False, auto_taper=True).copy()
    # 创建几个不同参数的电极示例
    print("=== 周期性电极示例 ===")

    # 示例1：基本参数
    electrode1 = waveguide_with_electrode()
    electrode1.pprint_ports()
    electrode1.plot()
    electrode1.draw_ports()
    # 显示第一个示例
    electrode1.show()