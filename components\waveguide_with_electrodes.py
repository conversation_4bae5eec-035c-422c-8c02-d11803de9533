import gdsfactory as gf
from gdsfactory.typings import LayerSpec
import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from components.electrode import ppln_electrode
from components.waveguide_tip import waveguide_with_tips
from pdk.layers import LAYER


@gf.cell
def waveguide_tip_with_ppln_electrodes(
    # Waveguide parameters
    wg_width: float = 0.5,
    wg_length: float | None = None,  # 中央波导长度，如果为None则自动计算
    tip_length: float = 20.0,
    tip_width: float = 0.2,
    taper_length: float = 15.0,
    offset: float = -40.0,
    bend_radius: float = 10.0,
    
    # Electrode parameters
    poling_length: float = 1000.0,
    period: float = 4.0,
    duty_cycle: float = 0.5,
    finger_length: float = 50.0,
    bus_width: float = 30.0,
    gap_to_wg: float = 10.0,
    fillet_radius: float = 0.10,
    bus_padding: float | None = None,
    port_width: float = 30,  # Port width (µm)
    port_spacing: float = 100,  # Spacing between ports (µm)
    pad_distance: float = 100.0,  # Distance from bus bar to electrical pads (µm)
    pad_width: float = 100.0,  # Width of electrical pads (µm)
) -> gf.Component:
    """Creates a waveguide with tapered tips and PPLN electrodes centered on it.

    Args:
        wg_width: Width of the waveguide (µm).
        wg_length: Length of the central waveguide (µm). If None, will be calculated to match electrode.
        tip_length: Length of the tip at each end (µm).
        tip_width: Width of the tip (µm).
        taper_length: Length of the taper between tip and waveguide (µm).
        left_offset: Left tip vertical offset (µm). Negative values go up.
        right_offset: Right tip vertical offset (µm). Negative values go up.
        bend_radius: Radius of the euler bends (µm).
        poling_length: Total length of poling region (µm).
        period: QPM period (µm).
        duty_cycle: Metal finger width to period ratio.
        finger_length: Length of electrode fingers (µm).
        bus_width: Width of bus bar (µm).
        gap_to_wg: Distance from finger tips to waveguide center (µm).
        fillet_radius: Corner rounding radius (µm).
        bus_padding: Extra length on each side of the bus bar. Defaults to period/2.
        center_component: Whether to center the final component.

    Returns:
        gf.Component: The component with the waveguide and electrodes.
    """
    c = gf.Component("waveguide_tip_with_ppln_electrodes")

    # Create and add the electrode component
    electrode_component = ppln_electrode(
        poling_length=poling_length,
        period=period,
        duty_cycle=duty_cycle,
        finger_length=finger_length,
        bus_width=bus_width,
        gap_to_wg=gap_to_wg,
        fillet_radius=fillet_radius,
        bus_padding=bus_padding,
        port_width=port_width,
        port_spacing=port_spacing,
        pad_distance=pad_distance,
        pad_width=pad_width,
    )
    electrode_ref = c.add_ref(electrode_component)
    
    # Calculate central waveguide length to match electrode length
    if wg_length is None:
        wg_length = electrode_component.xsize
    
    # Create the waveguide with tips
    waveguide_component = waveguide_with_tips(
        wg_length=wg_length,
        wg_width=wg_width,
        tip_length=tip_length,
        tip_width=tip_width,
        taper_length=taper_length,
        offset=offset,
        bend_radius=bend_radius,
        
    )
    waveguide_ref = c.add_ref(waveguide_component)
    waveguide_ref.dmovex(-(waveguide_ref.xmax+waveguide_ref.xmin)/2)
    waveguide_ref.dmovey(-(waveguide_ref.ymax+waveguide_ref.ymin)/2)
  
    # 确保电极和波导中心对齐
    # 波导已经居中，所以我们只需要确保电极也居中
   
    
    # Add ports from the waveguide component
    c.add_port("o1", port=waveguide_ref.ports["o1"])
    c.add_port("o2", port=waveguide_ref.ports["o2"])
    
    # Add electrical ports from the electrode component
    c.add_port("e1_left", port=electrode_ref.ports["e1_left"])
    c.add_port("e1_right", port=electrode_ref.ports["e1_right"])
    c.add_port("e2_left", port=electrode_ref.ports["e2_left"])
    c.add_port("e2_right", port=electrode_ref.ports["e2_right"])

    

    return c


if __name__ == "__main__":
    # 创建波导和电极组件
    component = waveguide_tip_with_ppln_electrodes()
    
    # 显示组件
    component.show()
