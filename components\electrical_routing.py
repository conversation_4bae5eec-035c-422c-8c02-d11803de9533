import gdsfactory as gf
import numpy as np

@gf.cell
def create_electrical_pads(
    pad_count: int = 6,
    pad_size: tuple = (100, 100),
    pad_spacing: float = 200,
    orientation: str = "up",  # "up" or "down"
    layer: tuple = (49, 0)  # Metal layer
):
    """创建一排电学焊盘
    
    Args:
        pad_count: 焊盘数量
        pad_size: 焊盘尺寸 (width, height)
        pad_spacing: 焊盘间距
        orientation: 端口方向 ("up" 朝上, "down" 朝下)
        layer: 金属层
    """
    c = gf.Component()
    
    # 计算总宽度和起始位置
    total_width = (pad_count - 1) * pad_spacing
    start_x = -total_width / 2
    
    # 根据方向设置端口orientation
    port_orientation = 90 if orientation == "up" else 270  # 90度朝上，270度朝下
    
    for i in range(pad_count):
        # 创建焊盘
        pad = gf.components.pad(
            size=pad_size, 
            layer=layer,
            port_inclusion=10,  # 端口伸入焊盘内部的距离
            port_orientation=port_orientation
        )
        
        # 放置焊盘
        pad_ref = c.add_ref(pad)
        pad_ref.center = (start_x + i * pad_spacing, 0)
        
        # 添加电学端口
        c.add_port(f"e{i+1}", port=pad_ref.ports["e1"])
    
    return c

def add_electrical_routing_to_smallpart(
    smallpart_component,
    routing_config=None
):
    """
    为smallpart添加电学走线：使用GDSFactory的route_bundle函数
    根据用户要求：上面的组件连接上方pads，下面的组件连接下方pads
    
    Args:
        smallpart_component: 原始smallpart组件
        routing_config: 走线配置
    """
    if routing_config is None:
        routing_config = {
            'enabled': True,
            'pad_count': 6,
            'pad_size': [100, 100],
            'pad_spacing': 200,
            'routing_layer': (49, 0),
            'pad_distance': 300
        }
    
    if not routing_config.get('enabled', False):
        return smallpart_component
    
    # 创建新的组件
    c = gf.Component(f"{smallpart_component.name}_with_routing")
    
    # 添加原始smallpart
    main_ref = c.add_ref(smallpart_component)
    
    # 收集所有电学端口
    electrical_ports = []
    
    # 从smallpart组件的端口中查找电学端口
    print("🔍 从smallpart组件查找电学端口...")
    if hasattr(smallpart_component, 'ports'):
        try:
            # 使用正确的方法获取端口
            all_ports = smallpart_component.ports.get_all_named()
            port_names = list(all_ports.keys())
                
            print(f"  🔍 smallpart 有端口: {port_names}")
            
            # 查找电学端口
            for port_name in port_names:
                try:
                    port = all_ports[port_name]
                    if hasattr(port, 'port_type') and port.port_type == "electrical":
                        electrical_ports.append((smallpart_component.name, port_name, port, port.center))
                        print(f"  ✅ 找到电学端口: {port_name} at {port.center}")
                except Exception as e:
                    print(f"  ⚠️ 端口 {port_name} 访问异常: {e}")
                    
        except Exception as e:
            print(f"⚠️ smallpart端口访问异常: {e}")
    
    if not electrical_ports:
        print("🔍 未发现电学端口，跳过走线")
        return smallpart_component
    
    print(f"🔍 发现 {len(electrical_ports)} 个电学端口: {[f'{name}.{port_name}' for name, port_name, _, _ in electrical_ports]}")
    
    # 创建上方和下方的焊盘
    pad_count = routing_config.get('pad_count', 6)
    pad_size = tuple(routing_config.get('pad_size', [100, 100]))
    pad_spacing = routing_config.get('pad_spacing', 200)
    pad_distance = routing_config.get('pad_distance', 300)
    
    # 上方焊盘（端口朝下）
    top_pads = create_electrical_pads(
        pad_count=pad_count,
        pad_size=pad_size, 
        pad_spacing=pad_spacing,
        orientation="down"
    )
    top_pads_ref = c.add_ref(top_pads)
    top_pads_ref.center = (main_ref.center[0], main_ref.ymax + pad_distance)
    
    # 下方焊盘（端口朝上）
    bottom_pads = create_electrical_pads(
        pad_count=pad_count,
        pad_size=pad_size,
        pad_spacing=pad_spacing, 
        orientation="up"
    )
    bottom_pads_ref = c.add_ref(bottom_pads)
    bottom_pads_ref.center = (main_ref.center[0], main_ref.ymin - pad_distance)
    
    # 简单的端口映射：上半部分组件连上方pad，下半部分连下方pad
    center_y = main_ref.center[1]
    top_ports = []
    bottom_ports = []
    
    for comp_name, port_name, port, abs_center in electrical_ports:
        # 判断端口在smallpart的上半部分还是下半部分
        if abs_center[1] > center_y:
            top_ports.append((comp_name, port_name, port, abs_center))
        else:
            bottom_ports.append((comp_name, port_name, port, abs_center))
    
    print(f"📍 端口分配: {len(top_ports)} 个上方端口, {len(bottom_ports)} 个下方端口")
    
    # 创建走线（专业版本 - 使用GDSFactory路由功能）
    routing_layer = routing_config.get('routing_layer', (49, 0))
    wire_width = routing_config.get('wire_width', 10)  # 走线宽度
    
    # 创建电学走线的cross_section
    electrical_xs = gf.cross_section.cross_section(
        width=wire_width,
        layer=routing_layer,
        port_names=("e1", "e2"),
        port_types=("electrical", "electrical")
    )
    
    print(f"🔧 使用走线宽度: {wire_width}, 层: {routing_layer}")
    
    # 连接上方端口到上方焊盘
    print(f"🔗 开始连接 {len(top_ports)} 个上方端口...")
    for i, (comp_name, port_name, port, abs_center) in enumerate(top_ports[:pad_count]):
        if i < pad_count:
            try:
                pad_port = top_pads_ref.ports[f"e{i+1}"]
                print(f"  🔌 连接端口 {port_name} -> 上方pad{i+1}")
                print(f"    📍 起点: {abs_center}")
                print(f"    📍 终点: {pad_port.center}")
                
                # 创建临时端口用于路由
                start_port = gf.Port(
                    name="start",
                    center=abs_center,
                    width=wire_width,
                    orientation=90,  # 向上
                    layer=routing_layer,
                    port_type="electrical"
                )
                
                end_port = gf.Port(
                    name="end", 
                    center=pad_port.center,
                    width=wire_width,
                    orientation=270,  # 向下
                    layer=routing_layer,
                    port_type="electrical"
                )
                
                # 使用GDSFactory的Manhattan路由
                try:
                    route = gf.routing.route_single(
                        start_port,
                        end_port,
                        cross_section=electrical_xs,
                        bend="bend_euler"
                    )
                    if route:
                        c.add_ref(route)
                        print(f"    ✅ 路由成功")
                    else:
                        print(f"    ❌ 路由失败，使用fallback方法")
                        # Fallback: 创建简单直线连接
                        create_simple_wire(c, abs_center, pad_port.center, wire_width, routing_layer)
                except Exception as route_error:
                    print(f"    ⚠️ 路由异常: {route_error}")
                    # Fallback: 创建简单直线连接
                    create_simple_wire(c, abs_center, pad_port.center, wire_width, routing_layer)
                    
            except Exception as e:
                print(f"  ❌ 连接端口 {port_name} 失败: {e}")
    
    # 连接下方端口到下方焊盘
    print(f"🔗 开始连接 {len(bottom_ports)} 个下方端口...")  
    for i, (comp_name, port_name, port, abs_center) in enumerate(bottom_ports[:pad_count]):
        if i < pad_count:
            try:
                pad_port = bottom_pads_ref.ports[f"e{i+1}"]
                print(f"  🔌 连接端口 {port_name} -> 下方pad{i+1}")
                
                # 创建临时端口用于路由
                start_port = gf.Port(
                    name="start",
                    center=abs_center,
                    width=wire_width,
                    orientation=270,  # 向下
                    layer=routing_layer,
                    port_type="electrical"
                )
                
                end_port = gf.Port(
                    name="end",
                    center=pad_port.center,
                    width=wire_width,
                    orientation=90,  # 向上
                    layer=routing_layer,
                    port_type="electrical"
                )
                
                # 使用GDSFactory的Manhattan路由
                try:
                    route = gf.routing.route_single(
                        start_port,
                        end_port,
                        cross_section=electrical_xs,
                        bend="bend_euler"
                    )
                    if route:
                        c.add_ref(route)
                        print(f"    ✅ 路由成功")
                    else:
                        print(f"    ❌ 路由失败，使用fallback方法")
                        create_simple_wire(c, abs_center, pad_port.center, wire_width, routing_layer)
                except Exception as route_error:
                    print(f"    ⚠️ 路由异常: {route_error}")
                    create_simple_wire(c, abs_center, pad_port.center, wire_width, routing_layer)
                    
            except Exception as e:
                print(f"  ❌ 连接端口 {port_name} 失败: {e}")
    
    # 添加所有焊盘端口
    for i in range(pad_count):
        c.add_port(f"top_e{i+1}", port=top_pads_ref.ports[f"e{i+1}"])
        c.add_port(f"bottom_e{i+1}", port=bottom_pads_ref.ports[f"e{i+1}"])
    
    print(f"✅ 完成电极走线: {len(top_ports)} 个上方端口, {len(bottom_ports)} 个下方端口")
    return c


def create_simple_wire(component, start_point, end_point, wire_width, layer):
    """
    创建简单的走线作为fallback方案
    使用直线或L型路径连接两点
    """
    try:
        # 计算路径点
        x1, y1 = start_point
        x2, y2 = end_point
        
        # 选择路径策略
        if abs(x2 - x1) > abs(y2 - y1):
            # 水平为主：水平-垂直-水平
            mid_x = (x1 + x2) / 2
            points = [(x1, y1), (mid_x, y1), (mid_x, y2), (x2, y2)]
        else:
            # 垂直为主：垂直-水平-垂直  
            mid_y = (y1 + y2) / 2
            points = [(x1, y1), (x1, mid_y), (x2, mid_y), (x2, y2)]
        
        # 为每段创建矩形
        for i in range(len(points) - 1):
            p1, p2 = points[i], points[i + 1]
            
            if p1[0] == p2[0]:  # 垂直线段
                x = p1[0]
                y_min, y_max = min(p1[1], p2[1]), max(p1[1], p2[1])
                rect_points = [
                    (x - wire_width/2, y_min),
                    (x + wire_width/2, y_min),
                    (x + wire_width/2, y_max),
                    (x - wire_width/2, y_max)
                ]
            else:  # 水平线段
                y = p1[1]
                x_min, x_max = min(p1[0], p2[0]), max(p1[0], p2[0])
                rect_points = [
                    (x_min, y - wire_width/2),
                    (x_max, y - wire_width/2),
                    (x_max, y + wire_width/2),
                    (x_min, y + wire_width/2)
                ]
            
            component.add_polygon(rect_points, layer=layer)
            
    except Exception as e:
        print(f"    ⚠️ 简单走线创建失败: {e}")


if __name__ == "__main__":
    # 测试焊盘创建
    pads = create_electrical_pads()
    pads.show()
