import sys
from pathlib import Path
LUMERICAL_API_PATH = "D:\\Program Files\\Lumerical\\v231\\api\\python"
if LUMERICAL_API_PATH not in sys.path:
        print(f"Adding Lumerical API path: {LUMERICAL_API_PATH}")
        sys.path.append(LUMERICAL_API_PATH)
try:
        import lumapi
        print("Successfully imported gplugins and lumapi.")
except ImportError as e:
        print(f"Error: Failed to import gplugins or lumapi. Ensure they are installed.")
        print(f"   Details: {e}")
        sys.exit(1)

sys.path.append(str(Path(__file__).parent))
from pcell_fdtd_wrapper import run_pcell_fdtd_with_dict

with lumapi.FDTD(hide=False) as session:
        params = {
                'offset': 35.0,
                'radius': 18.0,
                'wg_thickness': 0.28,
                'sidewall_angle': 82.0
        }
        sp=run_pcell_fdtd_with_dict(params,session)
        print(sp)

        