"""简单测试PDK的bend_points_distance问题"""

import gdsfactory as gf
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

print("=== PDK 测试 ===")

# 1. 检查默认PDK
print("\n1. 默认PDK状态:")
default_pdk = gf.get_active_pdk()
print(f"   名称: {default_pdk.name}")
print(f"   bend_points_distance: {default_pdk.bend_points_distance}")
print(f"   类型: {type(default_pdk.bend_points_distance)}")

# 2. 导入我们的PDK
print("\n2. 导入自定义PDK:")
try:
    from pdk import PDK, LAYERS
    print("   PDK导入成功")
    
    # 检查PDK状态
    current_pdk = gf.get_active_pdk()
    print(f"   当前PDK名称: {current_pdk.name}")
    print(f"   bend_points_distance: {current_pdk.bend_points_distance}")
    print(f"   类型: {type(current_pdk.bend_points_distance)}")
    
    # 3. 强制修复并测试
    print("\n3. 强制修复测试:")
    if not isinstance(current_pdk.bend_points_distance, (int, float)):
        print(f"   检测到问题: {current_pdk.bend_points_distance} (类型: {type(current_pdk.bend_points_distance)})")
        current_pdk.bend_points_distance = 0.02
        print(f"   修复后: {current_pdk.bend_points_distance} (类型: {type(current_pdk.bend_points_distance)})")
    else:
        print(f"   bend_points_distance正常: {current_pdk.bend_points_distance}")
    
    # 4. 测试简单组件创建
    print("\n4. 测试组件创建:")
    try:
        # 测试一个简单的弯曲组件
        bend = gf.components.bend_circular(radius=10.0, angle=90)
        print(f"   弯曲组件创建成功: {bend.name}")
    except Exception as e:
        print(f"   弯曲组件创建失败: {e}")
        
        # 尝试再次修复
        print("   尝试再次修复...")
        current_pdk.bend_points_distance = 0.02
        try:
            bend = gf.components.bend_circular(radius=10.0, angle=90)
            print(f"   修复后弯曲组件创建成功: {bend.name}")
        except Exception as e2:
            print(f"   修复后仍然失败: {e2}")
        
except Exception as e:
    print(f"   PDK导入失败: {e}")

print("\n=== 测试完成 ===")
