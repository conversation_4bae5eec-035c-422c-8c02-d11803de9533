# 波导模式分析器 (mode_clarify.py)

## 概述

`mode_clarify.py` 是一个用于分析 Lumerical 仿真输出的波导模式特性的 Python 模块。该模块经过重构，提供了清晰、简洁的接口，专门设计用于与 `write_sparameters_lumerical_mode` 模块配合使用。

## 主要功能

1. **偏振识别**：自动判断波导模式是 TE、TM 还是混合模式
2. **阶数确定**：通过分析场分布确定模式阶数 (m, n)
3. **传播特性计算**：计算相速度、群速度、群速度色散等关键参数
4. **批量分析**：支持多端口模式的批量分析
5. **报告生成**：生成格式化的分析报告
6. **端口模式提取**：通过端口 / 模式监视器的 mode solver 结果直接获取场分布（不重复运行 3D FDTD）

## 依赖库

```bash
pip install numpy scipy
```

## 核心类和函数

### WaveguideModeAnalyzer 类

主要的分析器类，提供完整的模式分析功能。

```python
from mode_clarify import WaveguideModeAnalyzer

# 创建分析器
analyzer = WaveguideModeAnalyzer(
    e_field_data,      # 电场数据字典
    h_field_data,      # 磁场数据字典  
    neff_data,         # 有效折射率数据字典
    target_wavelength  # 分析波长 (米)
)

# 执行完整分析
results = analyzer.analyze_mode()

# 生成报告
report = analyzer.get_mode_summary()
print(report)
```

### 便捷函数

#### analyze_lumerical_mode()

快速分析单个模式的便捷函数：

```python
from mode_clarify import analyze_lumerical_mode

results = analyze_lumerical_mode(
    e_field_data, 
    h_field_data, 
    neff_data,
    target_wavelength=1.55e-6
)
```

#### analyze_port_modes()

批量分析多个端口的模式：

```python
from mode_clarify import analyze_port_modes

# 需要 Lumerical 会话对象
results = analyze_port_modes(
    session, 
    port_names=['port 1', 'port 2'], 
    target_wavelength=1.55e-6
)
```

#### generate_mode_report()
#### get_port_mode_solver_data()

若已经在 Lumerical (FDTD / MODE) 中为端口添加了 eigenmode / mode expansion 监视器，并完成求解，可直接提取主模：

```python
from mode_clarify import get_port_mode_solver_data, analyze_lumerical_mode

mode_data = get_port_mode_solver_data(session, port_name='port 1', target_wavelength=1.55e-6)
result = analyze_lumerical_mode(
    mode_data['e_field'],
    mode_data['h_field'],
    mode_data['neff'],
    target_wavelength=1.55e-6
)
```

弃用函数：`extract_mode_data_from_lumerical_session` 已标记弃用，会发出 DeprecationWarning，请迁移到 `get_port_mode_solver_data`。


生成格式化报告：

```python
from mode_clarify import generate_mode_report

report = generate_mode_report(
    analysis_results, 
    output_file='mode_analysis_report.txt'  # 可选
)
```

## 数据格式要求

### 电场/磁场数据格式

```python
field_data = {
    'Ex': numpy_array,  # 2D 复数数组 [x, y] 
    'Ey': numpy_array,  # 2D 复数数组 [x, y]
    'Ez': numpy_array,  # 2D 复数数组 [x, y]
    'x': numpy_array,   # 1D 数组，x 坐标
    'y': numpy_array    # 1D 数组，y 坐标
}
```

### 有效折射率数据格式

```python
neff_data = {
    'lambda': numpy_array,  # 波长数组 (米)
    'neff': numpy_array     # 对应的有效折射率数组
}
```

## 与 write_sparameters_lumerical_mode 集成

### 基本集成示例

```python
import gdsfactory as gf
from write_sparameters_lumerical_mode import write_sparameters_lumerical
from mode_clarify import analyze_port_modes, generate_mode_report

# 创建组件
component = gf.components.mmi1x2()

# 运行 S 参数仿真（带模式分析）
def enhanced_simulation(component, session=None, **kwargs):
    # 标准 S 参数仿真
    sparameters = write_sparameters_lumerical(
        component=component,
        session=session,
        **kwargs
    )
    
    # 模式分析
    if session is not None:
        ports = component.ports.filter(port_type="optical")
        port_names = [f"port {i+1}" for i in range(len(ports))]
        
        mode_analysis = analyze_port_modes(session, port_names)
        
        # 生成报告
        report = generate_mode_report(mode_analysis, 'mode_report.txt')
        print("模式分析完成，报告已保存")
        
        return {
            'sparameters': sparameters,
            'mode_analysis': mode_analysis
        }
    
    return {'sparameters': sparameters}
```

### 高级集成示例

查看 `example_integration.py` 文件获取更详细的集成示例。

## 输出结果格式

### 分析结果字典

```python
{
    'analysis_success': True,
    'mode_name': 'TE_00',
    'polarization': {
        'type': 'TE',
        'te_ratio': 0.95,
        'tm_ratio': 0.05,
        'hybrid_parameter': 0.05
    },
    'mode_order': (0, 0),  # (m, n)
    'propagation': {
        'effective_index': 2.45,
        'phase_velocity': 1.22e8,
        'group_index': 2.48,
        'group_velocity': 1.21e8,
        'gvd_parameter': -120.5  # ps/(nm·km)
    },
    'analysis_wavelength': 1.55e-6
}
```

## 运行示例

```bash
# 运行基本示例
python mode_clarify.py

# 运行集成示例  
python example_integration.py
```

## 注意事项

1. **数据格式**：确保输入数据格式符合要求，特别是数组维度
2. **波长单位**：所有波长参数使用米 (m) 为单位
3. **复数场**：电场和磁场应为复数数组
4. **内存使用**：大型场分布数据可能占用较多内存
5. **数值精度**：色散计算使用样条插值，对数据噪声较为敏感

## 错误处理

模块提供了完善的错误处理机制：

- 输入数据验证
- 分析过程异常捕获
- 详细的错误信息返回

如果分析失败，结果将包含 `analysis_success: False` 和详细的错误信息。

## 配置 (ModeAnalysisConfig)

可通过 `ModeAnalysisConfig` 自定义分析行为：

```python
from mode_clarify import ModeAnalysisConfig, WaveguideModeAnalyzer

config = ModeAnalysisConfig(
    te_threshold=0.9,        # TE 判定阈值（Et占比）
    tm_threshold=0.9,        # TM 判定阈值（El占比）
    min_peak_rel_height=0.1, # 阶数检测相对高度阈值
    peak_prominence_rel=0.05,# 峰突出度相对阈值
    spline_s=1e-6,           # 色散拟合平滑
    spline_k=3,              # 样条阶数
    enforce_wavelength_in_range=True,
)

analyzer = WaveguideModeAnalyzer(e_field, h_field, neff_data, 1.55e-6, config=config)
```

新增偏振输出字段说明：

| 字段 | 含义 |
|------|------|
| et_ratio | 横向电场能量比例 (|Ex|^2+|Ey|^2) / 总能量 |
| el_ratio | 纵向电场能量比例 |Ez|^2 / 总能量 |
| te_ratio | 兼容字段=et_ratio |
| tm_ratio | 兼容字段=el_ratio |
| hybrid_parameter | 1 - max(te_ratio, tm_ratio) 越大越混合 |

## 版本历史

- v1.0: 重构原始代码，提供清晰的中文接口
- 优化了代码结构和性能
- 增强了与 write_sparameters_lumerical_mode 的集成能力
