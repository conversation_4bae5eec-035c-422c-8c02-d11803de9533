# Simulation Output Redirection - Changes Summary

## Overview
Modified three Python files in the simulation directory to redirect all console output to log files and save all generated plots to the `simulation_output` directory.

## Files Modified

### 1. `simulation/pcell_FDTD.py`
**Changes:**
- Added `import logging` 
- Added `setup_logging()` function to configure logging to save to `simulation_output/simulation.log`
- Modified `setup_lumerical_api()` to use logging instead of print statements
- Modified `qc_layer_stack()` to use logging instead of print statements  
- Modified `main()` function to:
  - Set up logging first thing after creating simulation_output directory
  - Replace all `print()` statements with `logger.info()`, `logger.error()`, etc.
  - Pass `output_dir=simulation_dir` parameter to `write_sparameters_lumerical()`

**Key Features:**
- Logging configuration writes to both file and console
- Log file: `simulation_output/simulation.log`
- Timestamp and log level included in all messages
- Configures gdsfactory logger to use same handlers

### 2. `simulation/write_sparameters_lumerical_mode.py`
**Changes:**
- Added `import logging`
- Added `output_dir: PathType | None = None` parameter to `write_sparameters_lumerical()` function
- Updated function docstring to document the new parameter
- Replaced `print()` statements with `logger.error()` and `logger.warning()`
- Modified calls to mode analysis functions to pass `output_dir` parameter:
  - `set_port_mode_solver_data(..., output_dir=output_dir)`
  - `get_port_mode_solver_data(..., output_dir=output_dir)`

### 3. `simulation/mode_clarify.py`
**Changes:**
- Added `import logging` and `from pathlib import Path`
- Modified function signatures to accept `output_dir: Optional[Path] = None`:
  - `set_port_mode_solver_data(..., output_dir=None)`
  - `get_port_mode_solver_data(..., output_dir=None)`
- Replaced all `print()` statements with appropriate logging calls:
  - `logger.info()` for informational messages
  - `logger.warning()` for warnings  
  - `logger.error()` for errors
- Added plot saving functionality:
  - **Mode field plots**: Saved as `mode_fields_{port_name}_{timestamp}.png`
  - **Neff vs wavelength plots**: Saved as `neff_vs_wavelength_{port_name}_{timestamp}.png`
  - **Port results plots**: Saved as `port_results_{port_name}_{timestamp}.png`
- All plots saved to the `output_dir` with high DPI (300) and tight bounding boxes
- Plots still display on screen (`plt.show(block=False)`) in addition to being saved

## Output Structure
```
simulation_output/
├── simulation.log                           # All console output and logs
├── mode_fields_FDTD_ports_o1_123456.png   # Mode field distribution plots
├── neff_vs_wavelength_FDTD_ports_o1_123456.png  # Effective index vs wavelength
├── port_results_FDTD_ports_o1_123456.png  # Port analysis results
├── PCELL_FDTD.fsp                          # Original Lumerical project file
└── [other existing files...]
```

## Key Features

### Logging System
- **Comprehensive logging**: All console output redirected to log file
- **Dual output**: Messages appear both in console and log file
- **Structured format**: Timestamp, module name, log level, and message
- **UTF-8 encoding**: Supports Chinese characters in log messages
- **Automatic file creation**: Log file created in simulation_output directory

### Plot Saving
- **Automatic saving**: All matplotlib plots automatically saved to files
- **High quality**: 300 DPI resolution with tight bounding boxes
- **Unique filenames**: Timestamp-based naming prevents overwrites
- **Multiple plot types**: Mode fields, dispersion curves, and analysis results
- **Non-blocking display**: Plots still show on screen for interactive use

### Backward Compatibility
- All existing functionality preserved
- Optional parameters with sensible defaults
- Graceful handling when output_dir is None
- No breaking changes to existing API

## Usage
Run the main script as before:
```bash
cd simulation
python pcell_FDTD.py
```

All output will be automatically saved to the `simulation_output` directory.
