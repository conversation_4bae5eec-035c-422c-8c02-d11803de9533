"""
Minimal PSO-FDTD Integration

Simple optimization system with real-time visualization.
"""

import sys
from pathlib import Path
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from optimization.realtime_pso import RealtimePSO
from optimization.fdtd_interface import create_fdtd_objective_function


def run_optimization(use_real_fdtd: bool = False):
    """Run PSO optimization with FDTD simulation."""
    print("="*60)
    print("🚀 PSO OPTIMIZATION WITH FDTD SIMULATION")
    print("="*60)

    # Define optimization parameter bounds
    param_bounds = {
        'offset': (25.0, 45.0),
        'radius': (15.0, 25.0),
    }

    print(f"📋 Optimization Parameters:")
    for param, (min_val, max_val) in param_bounds.items():
        print(f"   {param}: [{min_val}, {max_val}]")

    # Create FDTD objective function
    print(f"\n🚀 Creating {'real FDTD' if use_real_fdtd else 'mock'} objective function...")
    objective_func = create_fdtd_objective_function(use_real_fdtd=use_real_fdtd)

    # Configure PSO parameters
    num_particles = 10 if use_real_fdtd else 20
    max_iterations = 15 if use_real_fdtd else 25

    print(f"   Particles: {num_particles}, Iterations: {max_iterations}")
    print(f"\n🖥️  Real-time visualization will show:")
    print("    📈 FOM evolution")
    print("    📋 Best parameters")
    print("    📊 Parameter evolution")
    print("    📊 FOM distribution")

    # Create and run PSO optimization with responsive visualization
    pso = RealtimePSO(
        param_bounds=param_bounds,
        objective_func=objective_func,
        num_particles=num_particles,
        max_iterations=max_iterations,
        w=0.7,
        c1=1.5,
        c2=1.5,
        use_responsive_viz=True,  # Enable responsive visualization
        max_workers=1  # Keep sequential for FDTD session safety
    )

    print(f"\n🎯 Starting PSO optimization...")

    try:
        # Run optimization
        best_params, best_fitness = pso.optimize()

        print(f"\n✅ OPTIMIZATION COMPLETED!")
        print("="*50)
        print(f"🏆 Best insertion loss: {best_fitness:.4f} dB")
        print(f"📋 Best parameters:")
        for param, value in best_params.items():
            print(f"   {param}: {value:.4f}")

        # Cleanup
        if hasattr(objective_func, 'fdtd_interface'):
            objective_func.fdtd_interface.close()

    except KeyboardInterrupt:
        print(f"\n⚠️  Optimization interrupted by user")
    except Exception as e:
        print(f"\n❌ Optimization failed: {e}")

    print(f"\n🏁 Optimization completed!")


if __name__ == "__main__":
    #import argparse

    #parser = argparse.ArgumentParser(description="Minimal PSO-FDTD Integration")
    #parser.add_argument("--real-fdtd", action="store_true",
    #                   help="Use real FDTD simulation instead of mock")

    #args = parser.parse_args()

    run_optimization(use_real_fdtd=False)
