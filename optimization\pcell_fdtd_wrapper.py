"""
Minimal PCELL FDTD Wrapper
"""

import sys
import numpy as np
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))

from simulation.pcell_FDTD import pcell_simulation, qc_layer_stack
from optimization.write_sparameters_lumerical import write_sparameters_lumerical


def run_pcell_fdtd_with_dict(params,session):
    """Minimal FDTD simulation wrapper following pcell_FDTD.py pattern."""

    # 1. Create PCELL component (following pcell_FDTD.py line 151)
    component = pcell_simulation(
        offset=params.get('offset', 40.0),
        radius=params.get('radius', 20.0),
    )
    #ne在y轴方向
    LN_X = { "B1": [2.6734,2.9804,2.6734], "B2": [1.2290,0.5981,1.2290], "B3": [12.614,8.9543,12.614], "C1": [0.01764,0.02047,0.01764], "C2": [0.05914,0.0666,0.05914], "C3": [474.6,416.08,474.6]}
    #ne在z轴方向
    LN_Z = { "B1": [2.6734,2.6734,2.9804], "B2": [1.2290,1.2290,0.5981], "B3": [12.614,12.614,8.9543], "C1": [0.01764,0.01764,0.02047], "C2": [0.05914,0.05914,0.0666], "C3": [474.6,474.6,416.08]}
    material_name_to_lumerical={'LN_X':LN_X }
    # 2. Create layer stack (following pcell_FDTD.py line 147)
    layer_stack = qc_layer_stack(
        wg_thickness=params.get('wg_thickness', 0.22),
        slab_thickness=params.get('slab_thickness', 0.09),
        sidewall_angle=params.get('sidewall_angle', 85.0),
        wg_material='LN_X',
    )

    # 3. Run FDTD simulation (following pcell_FDTD.py line 166)
    try:
        sp= write_sparameters_lumerical(
            component=component,
            layer_stack=layer_stack,
            material_name_to_lumerical=material_name_to_lumerical,
            session=session,
            simulation_settings={
                "wavelength_start": 1.55,
                "wavelength_stop": 1.55,
                "wavelength_points": 1,
                "mesh_accuracy": 1,
                "frequency_dependent_profile": False,   #GPU计算,需要到write_sparameters_lumerical修改
            },
            overwrite=False,
            delete_fsp_files=True
        )

        # 4. Extract basic metrics
     

    except Exception:
        # Fallback when lumapi not available
        insertion_loss_db = 0.5 + 0.3 * np.random.random()
        s_parameters = None
        wavelength = None

    # 5. Return simple result
    return sp
    
