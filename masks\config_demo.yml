# 🐱 实用高级配置 - 两种参数扫描方式演示

# ===== 芯片基本信息 =====
chip_settings:
  name: "demo_parameter_sweep"
  size: [12000.0, 10000.0]
  spacing: [0, 000]

# ===== PCells库定义 =====
pcells:
  ring_heater:
    component: "ring_with_electrode_with_heater_with_tip"
    defaults:
      ring_width: 1.5
      waveguide_width: 1
      gap: 0.6
      coupling_angle_coverage: 10
      ring_radius: 99.25
      heater_width: 3
      length_left: 100
      length_total: 600
      
  simple_electrode:
    component: "waveguide_tip_with_ppln_electrodes"
    defaults:
      wg_width: 2
      tip_length: 200.0
      tip_width: 2
      taper_length: 300
      bend_radius: 100
      offset: -300
      poling_length: 1000.0
      period: 4.66
      duty_cycle: 0.2
      finger_length: 20.0
      bus_width: 20.0
      gap_to_wg: 5.0
      bus_padding: 5
      
  thermal_ring:
    component: "thermal_heater_ring"
    defaults:
      ring_radius: 50.0
      gap: 0.2
      ring_width: 0.5
      heater_width: 2.0

# ===== 布局设置 =====
layout:
  grid:
    rows: 2                    # 2行演示
    cols: 2                    # 2列演示
    row_spacing: 500
    col_spacing: 1200
    
  labels:
    enabled: true
    format: "{col_letter}{row_number}"
    default_offset: 50
    
  internal:
    default_spacing: 00
    default_count: 3
    
  # 列定义
  columns:
    # ========== 第1列：同一smallpart内参数扫描 ==========
    col_0:
      pcell_type: ring_heater
      description: "同一smallpart内length_left扫描"
      base_params: {}
      variations:
        # 第1行：length_left参数扫描 (6个组件，紧密排列)
        - parameter_sweep:
            - {length_left: 100}
            - {length_left: 300} 
            - {length_left: 500} 
            - {length_left: 100}
            - {length_left: 300} 
            - {length_left: 500} 
          count: 6
          spacing: -200                    # 紧密间距
          position_offset: [100, 50]       # X右移100, Y上移50
          electrical_routing:
            enabled: true
            pad_count: 6
            pad_size: [100, 100]
            pad_spacing: 200
            pad_distance: 300
        # 第2行：多参数组合扫描 (4个组件，宽松排列)
        - parameter_sweep:
            - {length_left: 150, gap: 0.4}
            - {length_left: 250, gap: 0.6}
            - {length_left: 350, gap: 0.8}
            - {length_left: 400, gap: 1.0}
          count: 4
          spacing: -600                    # 宽松间距
          position_offset: [-200, -100]    # X左移200, Y下移100
      position_offset: [0, 0]
      
    # ========== 第2列：同一列不同PCells ==========
    col_1:
      multi_pcells: true
      description: "同一列不同PCells"
      variations:
        # 第1行：环形谐振器 (紧凑排列)
        - pcell_type: ring_heater
          params: {length_left: 100, ring_radius: 50}
          spacing: -300                    # 紧凑间距
          position_offset: [50, 200]       # X右移50, Y上移200
        # 第2行：简单电极 (超宽松排列)
        - pcell_type: simple_electrode  
          params: {wg_width: 1.5, poling_length: 800}
          spacing: -800                    # 超宽松间距
          position_offset: [-100, -200]    # X左移100, Y下移200
      position_offset: [0, 0]

# ===== 标记设置 =====
marks:
  alignment:
    enabled: true
    layer: "MARK" 
    margins: [1500, 1000, 1000, 1500]
    spacing: 1000
    cross_size: [50, 2]
    
  dicing:
    smallpart_enabled: true
    chip_enabled: true
    layer: "DICING"
    mark_size: [50, 20]
    boundary_offset: [-200, 200]

# ===== 输出设置 =====
output:
  path: "demo_sweep.gds"
  show_gds: true
