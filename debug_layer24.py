"""调试layer 24问题"""

import gdsfactory as gf
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

print("=== 调试layer 24问题 ===")

# 导入自定义PDK
from pdk import PDK, LAYERS

# 检查当前PDK的层定义
current_pdk = gf.get_active_pdk()
print(f"当前PDK: {current_pdk.name}")

print("\n层定义:")
layer_dict = {
    'SUBSTRATE': LAYERS.SUBSTRATE,
    'WG': LAYERS.WG,
    'METAL': LAYERS.METAL,
    'HEATER': LAYERS.HEATER,
    'DICING': LAYERS.DICING,
    'TEXT': LAYERS.TEXT,
    'MARK': LAYERS.MARK
}

for name, layer in layer_dict.items():
    print(f"  {name}: {layer}")

print(f"\nlayer 24对应的层:")
# 查找layer 24
found = False
for name, layer in layer_dict.items():
    if layer == (24, 0):
        print(f"  找到: {name} = {layer}")
        found = True
        break
if not found:
    print("  未找到layer (24, 0)")
    print("  layer 24可能来自默认PDK")

# 检查默认PDK中的layer 24
print(f"\n检查默认PDK中是否有layer 24:")
try:
    # 创建一个使用layer 24的组件
    test_comp = gf.components.rectangle(size=(10, 10), layer=(24, 0))
    print(f"  可以创建layer 24的组件: {test_comp.name}")
except Exception as e:
    print(f"  无法创建layer 24的组件: {e}")

# 检查cross_section中的layer
print(f"\n检查cross_section:")
try:
    # 检查metal_routing cross_section
    metal_cs = gf.get_cross_section("metal_routing")
    print(f"  metal_routing cross_section layer: {metal_cs.layer}")
except Exception as e:
    print(f"  无法获取metal_routing cross_section: {e}")

# 检查默认PDK中的layer定义
print(f"\n检查默认PDK中的layer 24:")
try:
    # 获取默认PDK
    default_pdk = gf.pdk.get_pdk("generic")
    if hasattr(default_pdk, 'layers'):
        for layer_name in dir(default_pdk.layers):
            if not layer_name.startswith('_'):
                layer_value = getattr(default_pdk.layers, layer_name)
                if layer_value == (24, 0):
                    print(f"  默认PDK中layer 24是: {layer_name}")
                    break
        else:
            print("  默认PDK中未找到layer (24, 0)")
    else:
        print("  默认PDK没有layers属性")
except Exception as e:
    print(f"  检查默认PDK失败: {e}")

print("\n=== 调试完成 ===")
