#!/usr/bin/env python3
"""测试演示配置解析"""

from masks.mixed_config_parser import parse_mixed_config
import yaml

try:
    config = yaml.safe_load(open('masks/config_demo.yml', encoding='utf-8'))
    result = parse_mixed_config(config)
    print('✅ 演示配置解析成功!')
    print(f'生成了 {len(result["smallparts"])} 个smallparts')
    
    for sp_name, sp_config in result['smallparts'].items():
        print(f'\n{sp_name}:')
        if 'parameter_sweep' in sp_config:
            print(f'  parameter_sweep模式: {sp_config["count"]} 个组件')
            for i, params in enumerate(sp_config['parameter_sweep']):
                length_left = params.get('length_left', 'N/A')
                gap = params.get('gap', 'N/A')
                print(f'    组件{i+1}: length_left={length_left}, gap={gap}')
        else:
            print(f'  标准模式: {sp_config["count"]} 个组件')
            print(f'  pcell={sp_config["pcell_name"]}')
            
except Exception as e:
    print(f'❌ 错误: {e}')
    import traceback
    traceback.print_exc()
