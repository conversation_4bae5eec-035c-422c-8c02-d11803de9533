# 🐱 简单配置文件 - 单一PCells类型专用

# ===== 芯片基本信息 =====
chip:
  name: "simple_chip"
  size: [12000.0, 12000.0]  # [宽度, 高度] µm

# ===== PCells设置 =====
pcell:
  type: "waveguide_tip_with_ppln_electrodes"  # 使用的组件类型
  count: 8                                    # 每个smallpart的组件数量
  
  # 默认参数（所有位置共用）
  defaults:
    # 波导参数
    wg_width: 2
    tip_length: 200.0
    tip_width: 2
    taper_length: 300
    bend_radius: 100
    offset: -300
    
    # 电极参数
    period: 4.66
    finger_length: 20.0
    bus_width: 20.0
    gap_to_wg: 5.0
    bus_padding: 5

# ===== 布局设置（集中管理）=====
layout:
  # 网格设置
  grid:
    rows: 3                    # 行数
    cols: 4                    # 列数
    row_spacing: 230           # 行间距 µm
    col_spacing: 0             # 列间距 µm（紧挨着）
    
  # 标签设置
  labels:
    enabled: true
    format: "{col_letter}{row_number}"  # A1, B2格式
    
  # smallpart内部布局
  internal:
    spacing: -250              # 默认smallpart内部组件间距
    
  # ===== 布局矩阵（3行4列，方便可视化修改）=====
  # X偏移矩阵（每个smallpart的X方向偏移）
  x_offsets: [
    [ 200,    0, -200, -400],  # 第1行: A1, B1, C1, D1
    [ 200,    0, -200, -400],  # 第2行: A2, B2, C2, D2  
    [ 200,    0, -200, -400]   # 第3行: A3, B3, C3, D3
  ]
  
  # Y偏移矩阵（每个smallpart的Y方向偏移）
  y_offsets: [
    [   0,    0,    0,    0],  # 第1行: A1, B1, C1, D1
    [   0,    0,    0,    0],  # 第2行: A2, B2, C2, D2
    [   0,    0,    0,    0]   # 第3行: A3, B3, C3, D3
  ]
  
  # 每个smallpart的器件数量矩阵
  count_matrix: [
    [   8,    8,    8,    8],  # 第1行: A1, B1, C1, D1
    [   8,    8,    8,    8],  # 第2行: A2, B2, C2, D2
    [   6,    6,    6,    6]   # 第3行: A3, B3, C3, D3
  ]
  
  # 标签偏移矩阵
  label_offsets: [
    [  30,   30,   30,  100],  # 第1行: A1, B1, C1, D1
    [  30,   30,   30,  100],  # 第2行: A2, B2, C2, D2
    [  30,   30,   30,  100]   # 第3行: A3, B3, C3, D3
  ]
  
  # 内部间距矩阵（每个smallpart内部器件的间距）
  internal_spacing_matrix: [
    [-250, -250, -250,  -50],  # 第1行: A1, B1, C1, D1
    [-250, -250, -250,  -50],  # 第2行: A2, B2, C2, D2
    [-250, -250, -250,  -50]   # 第3行: A3, B3, C3, D3
  ]

# ===== 参数扫描定义 =====
parameter_sweep:
  # ===== 按位置定义参数变化（只需定义器件参数，布局参数在上面矩阵中）=====
  # 格式: [row, col]: {参数名: 值}
  
  # 第一行：波导长度 + 极化长度扫描
  "[0, 0]": {wg_length: 800, poling_length: 500, duty_cycle: 0.2}
  "[0, 1]": {wg_length: 1200, poling_length: 1000, duty_cycle: 0.2}
  "[0, 2]": {wg_length: 1200, poling_length: 1000, duty_cycle: 0.2, gap_to_wg: 7.5}
  "[0, 3]": {wg_length: 1200, poling_length: 1500, duty_cycle: 0.2, offset: 0}
  
  # 第二行：占空比扫描
  "[1, 0]": {wg_length: 800, poling_length: 500, duty_cycle: 0.25}
  "[1, 1]": {wg_length: 1200, poling_length: 1000, duty_cycle: 0.25}
  "[1, 2]": {wg_length: 1200, poling_length: 1000, duty_cycle: 0.25, bus_padding: 7.5}
  "[1, 3]": {wg_length: 1200, poling_length: 1500, duty_cycle: 0.25, offset: 0}
  
  # 第三行：高占空比测试
  "[2, 0]": {wg_length: 800, poling_length: 500, duty_cycle: 0.3}
  "[2, 1]": {wg_length: 1200, poling_length: 1000, duty_cycle: 0.3}
  "[2, 2]": {wg_length: 1200, poling_length: 1000, duty_cycle: 0.3, gap_to_wg: 7.5}
  "[2, 3]": {wg_length: 1200, poling_length: 1500, duty_cycle: 0.3, offset: 0}

# ===== 标记设置 =====
# 对准标记
alignment_marks:
  enabled: true
  layer: "MARK"
  margin_xx: 1500  # 上边距
  margin_xy: 1000  # 下边距
  margin_yx: 1000  # 左边距
  margin_yy: 1500  # 右边距
  spacing: 1000
  cross_length: 50  # 十字长度
  cross_width: 2   # 十字宽度
  
# 芯片级切割标记  
chip_dicing:
  enabled: true
  layer: "DICING"
  mark_width: 50.0
  mark_height: 20.0
  boundary_xoffset: -200.0
  boundary_yoffset: 200.0

# 小part级切割标记
smallpart_dicing:
  enabled: false
  layer: "DICING"
  size: [50, 20]

# ===== 输出设置 =====
output:
  path: "simple_chip.gds"
  show_gds: true
