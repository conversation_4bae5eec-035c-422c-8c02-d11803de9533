#!/usr/bin/env python3
"""
混合PCells配置解析器 - 专门处理多PCells类型的配置文件 (config_mixed.yml)
"""

def parse_mixed_config(config_data):
    """解析多PCells配置格式 (config_mixed.yml)"""
    try:
        chip_settings = config_data['chip_settings']
        pcells = config_data['pcells']
        layout = config_data['layout']
        columns = layout['columns']
        
        rows = layout['grid']['rows']
        cols = layout['grid']['cols']
        
        # 构建标准格式配置
        standard_config = {
            'chip_settings': chip_settings,
            'smallparts_layout': {},
            'smallparts': {}
        }
        
        # 生成每个smallpart的配置
        for row in range(rows):
            for col in range(cols):
                smallpart_name = f"smallpart{row * cols + col + 1}"
                
                # 获取对应列的配置
                col_key = f"col_{col}"
                if col_key not in columns:
                    continue
                    
                col_config = columns[col_key]
                
                # 检查是否是多PCells模式
                if col_config.get('multi_pcells', False):
                    # 处理同一列不同PCells的情况
                    variations = col_config.get('variations', [])
                    if row < len(variations):
                        variation = variations[row]
                        pcell_type = variation.get('pcell_type')
                        if pcell_type not in pcells:
                            print(f"警告: PCells类型 {pcell_type} 未定义，跳过")
                            continue
                        
                        # 获取该PCells的默认参数
                        pcell_defaults = pcells[pcell_type].get('defaults', {})
                        final_params = pcell_defaults.copy()
                        final_params.update(variation.get('params', {}))
                        
                        # 设置smallpart配置（单个组件）
                        component_name = pcells[pcell_type]['component']
                        smallpart_config = {
                            'pcell_name': component_name,
                            'count': 1,  # 单个组件
                            'add_dicing_marks': False,
                            'dicing_offset_x': -100.0,
                            'dicing_offset_y': 100.0,
                            'parameters': final_params
                        }
                else:
                    # 标准模式：同一PCells类型
                    pcell_type = col_config['pcell_type']
                    
                    # 检查PCells定义是否存在
                    if pcell_type not in pcells:
                        print(f"警告: PCells类型 {pcell_type} 未定义，跳过")
                        continue
                    
                    # 获取基础参数
                    base_params = col_config.get('base_params', {})
                    pcell_defaults = pcells[pcell_type].get('defaults', {})
                    
                    # 合并参数
                    final_params = pcell_defaults.copy()
                    final_params.update(base_params)
                    
                    # 处理variations
                    variations = col_config.get('variations', [{}])
                    if variations and row < len(variations):
                        variation = variations[row]
                        
                        # 检查是否是parameter_sweep模式
                        if 'parameter_sweep' in variation:
                            # 同一smallpart内参数扫描
                            sweep_params = variation['parameter_sweep']
                            # 获取组件数量（优先使用variation中的count，其次是sweep_params长度）
                            component_count = variation.get('count', len(sweep_params))
                            
                            # 创建多组件配置
                            smallpart_config = {
                                'pcell_name': pcells[pcell_type]['component'],
                                'count': component_count,
                                'add_dicing_marks': False,
                                'dicing_offset_x': -100.0,
                                'dicing_offset_y': 100.0,
                                'parameter_sweep': []  # 存储多个参数组
                            }
                            
                            # 为每个组件创建参数组
                            for sweep_param in sweep_params:
                                component_params = final_params.copy()
                                component_params.update(sweep_param)
                                smallpart_config['parameter_sweep'].append(component_params)
                            
                            # 添加电极走线配置支持
                            if 'electrical_routing' in variation:
                                smallpart_config['electrical_routing'] = variation['electrical_routing']
                        else:
                            # 标准单参数变化
                            if variation:
                                final_params.update(variation)
                            
                            smallpart_config = {
                                'pcell_name': pcells[pcell_type]['component'],
                                'count': layout_override.get('count', layout.get('internal', {}).get('default_count', 8)),
                                'add_dicing_marks': False,
                                'dicing_offset_x': -100.0,
                                'dicing_offset_y': 100.0,
                                'parameters': final_params
                            }
                
                # 获取位置偏移和布局覆盖（优先使用variation级别的设置）
                if variations and row < len(variations):
                    current_variation = variations[row]
                    # variation级别的position_offset优先
                    variation_offset = current_variation.get('position_offset', [0, 0])
                    # variation级别的spacing优先
                    variation_spacing = current_variation.get('spacing', layout.get('internal', {}).get('default_spacing', -250))
                else:
                    variation_offset = [0, 0] 
                    variation_spacing = layout.get('internal', {}).get('default_spacing', -250)
                
                # 列级别的position_offset作为基础偏移
                col_offset = col_config.get('position_offset', [0, 0])
                layout_override = col_config.get('layout_override', {})
                
                # 组合偏移：列偏移 + variation偏移
                final_x_offset = col_offset[0] + variation_offset[0]
                final_y_offset = col_offset[1] + variation_offset[1]
                
                # 设置布局配置
                standard_config['smallparts_layout'][smallpart_name] = {
                    'row': row,
                    'col': col,
                    'label_offset': layout_override.get('label_offset', layout.get('internal', {}).get('default_offset', 30)),
                    'x_offset': final_x_offset,
                    'y_offset': final_y_offset,
                    'grid': {
                        'spacing': [0, variation_spacing]
                    }
                }
                
                # 设置smallpart配置
                standard_config['smallparts'][smallpart_name] = smallpart_config
        
        # 添加标记配置
        if 'marks' in config_data:
            marks = config_data['marks']
            if 'alignment' in marks:
                alignment_marks = marks['alignment'].copy()
                # 转换参数名映射
                if 'margins' in alignment_marks:
                    margins = alignment_marks.pop('margins')
                    if len(margins) >= 4:
                        alignment_marks['margin_xx'] = margins[0]
                        alignment_marks['margin_xy'] = margins[1] 
                        alignment_marks['margin_yx'] = margins[2]
                        alignment_marks['margin_yy'] = margins[3]
                # 转换cross_size为cross_length和cross_width
                if 'cross_size' in alignment_marks:
                    cross_size = alignment_marks.pop('cross_size')
                    if len(cross_size) >= 2:
                        alignment_marks['cross_length'] = cross_size[0]
                        alignment_marks['cross_width'] = cross_size[1]
                standard_config['alignment_marks'] = alignment_marks
            if 'dicing' in marks:
                if marks['dicing'].get('chip_enabled', False):
                    standard_config['chip_dicing'] = {
                        'enabled': True,
                        'layer': marks['dicing']['layer'],
                        'mark_width': marks['dicing']['mark_size'][0],
                        'mark_height': marks['dicing']['mark_size'][1],
                        'boundary_xoffset': marks['dicing']['boundary_offset'][0],
                        'boundary_yoffset': marks['dicing']['boundary_offset'][1]
                    }
                if marks['dicing'].get('smallpart_enabled', False):
                    standard_config['smallpart_dicing'] = {
                        'enabled': True,
                        'layer': marks['dicing']['layer'],
                        'size': marks['dicing']['mark_size']
                    }
        
        return standard_config
        
    except Exception as e:
        print(f"❌ 解析多PCells配置失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def is_mixed_config(config_data):
    """检测是否为混合PCells格式配置"""
    if not isinstance(config_data, dict):
        return False
        
    # 检查关键结构
    required_keys = ['chip_settings', 'pcells', 'layout']
    if not all(key in config_data for key in required_keys):
        return False
    
    # 检查layout结构中是否有columns
    layout = config_data.get('layout', {})
    if 'columns' not in layout:
        return False
    
    # 检查pcells结构
    pcells = config_data.get('pcells', {})
    if not isinstance(pcells, dict) or not pcells:
        return False
    
    return True


if __name__ == "__main__":
    import sys
    import yaml
    from pathlib import Path
    
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if is_mixed_config(config_data):
                result = parse_mixed_config(config_data)
                if result:
                    print("✅ 解析成功!")
                    print(f"📊 生成了 {len(result.get('smallparts', {}))} 个smallparts")
                else:
                    print("❌ 解析失败!")
            else:
                print("❌ 不是有效的混合PCells配置格式")
        except Exception as e:
            print(f"❌ 配置文件读取失败: {e}")
    else:
        print("用法: python mixed_config_parser.py <config_file>")
