#!/usr/bin/env python3
"""测试间距和偏移配置解析"""

from masks.mixed_config_parser import parse_mixed_config
import yaml

try:
    config = yaml.safe_load(open('masks/config_demo.yml', encoding='utf-8'))
    result = parse_mixed_config(config)
    print('✅ 间距和偏移配置解析成功!')
    print(f'生成了 {len(result["smallparts"])} 个smallparts')
    
    for sp_name, sp_config in result['smallparts'].items():
        layout_config = result['smallparts_layout'][sp_name]
        
        print(f'\n{sp_name}:')
        print(f'  位置偏移: X={layout_config["x_offset"]}, Y={layout_config["y_offset"]}')
        print(f'  间距设置: {layout_config["grid"]["spacing"][1]}')
        
        if 'parameter_sweep' in sp_config:
            print(f'  parameter_sweep模式: {sp_config["count"]} 个组件')
        else:
            print(f'  标准模式: {sp_config["count"]} 个组件')
            print(f'  pcell={sp_config["pcell_name"]}')
            
except Exception as e:
    print(f'❌ 错误: {e}')
    import traceback
    traceback.print_exc()
