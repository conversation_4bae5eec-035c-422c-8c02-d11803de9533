"""
Enhanced PSO Optimization Module for PCELL FDTD Simulation

Clean PSO framework with enhanced real-time visualization.

Usage:
    from optimization.realtime_pso import RealtimePSO
    from optimization.mock_fdtd import create_objective_function

    # Define parameter bounds
    param_bounds = {'offset': (25.0, 45.0), 'radius': (15.0, 25.0)}

    # Create objective function
    objective_func = create_objective_function()

    # Run optimization with enhanced real-time visualization
    pso = RealtimePSO(param_bounds, objective_func)
    best_params, best_fitness = pso.optimize()
"""

from .realtime_pso import RealtimePSO, ResponsivePSOVisualizer
from .mock_fdtd import create_objective_function, mock_fdtd_simulation
from .fdtd_interface import (
    FDTDInterface,
    create_fdtd_objective_function
)
from .pcell_fdtd_wrapper import run_pcell_fdtd_with_dict

__version__ = "3.2.0"
__author__ = "PCELL Optimization Team"

__all__ = [
    "RealtimePSO",
    "ResponsivePSOVisualizer",
    "create_objective_function",
    "mock_fdtd_simulation",
    "FDTDInterface",
    "create_fdtd_objective_function",
    "run_pcell_fdtd_with_dict",
]
