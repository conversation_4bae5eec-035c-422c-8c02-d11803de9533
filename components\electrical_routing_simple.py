"""
电学走线模块 - 使用GDSFactory的route_bundle函数
根据用户要求：使用简单映射规则，上面的组件连接上方pads
"""

import gdsfactory as gf
from typing import List, Tuple, Optional, Dict


def add_electrical_routing_to_smallpart(smallpart_component, routing_config=None):
    """
    为smallpart添加电学走线：使用GDSFactory的route_bundle函数
    根据用户要求：上面的组件连接上方pads，下面的组件连接下方pads
    
    Args:
        smallpart_component: 原始smallpart组件
        routing_config: 走线配置
    """
    if routing_config is None:
        routing_config = {
            'enabled': True,
            'pad_count': 6,
            'pad_size': [100, 100],
            'pad_spacing': 200,
            'routing_layer': (49, 0),
            'pad_distance': 300
        }
    
    if not routing_config.get('enabled', False):
        return smallpart_component
    
    # 创建新的组件
    c = gf.Component(f"{smallpart_component.name}_with_routing")
    
    # 添加原始smallpart
    main_ref = c.add_ref(smallpart_component)
    
    # 收集所有电学端口
    electrical_ports = []
    
    # 从smallpart组件的端口中查找电学端口
    print("🔍 从smallpart组件查找电学端口...")
    if hasattr(smallpart_component, 'ports'):
        try:
            # 使用正确的方法获取端口
            all_ports = smallpart_component.ports.get_all_named()
            port_names = list(all_ports.keys())
                
            print(f"  🔍 smallpart 有端口: {port_names}")
            
            # 查找电学端口
            for port_name in port_names:
                try:
                    port = all_ports[port_name]
                    if hasattr(port, 'port_type') and port.port_type == "electrical":
                        electrical_ports.append((port_name, port))
                        print(f"  ✅ 找到电学端口: {port_name} at {port.center}")
                except Exception as e:
                    print(f"  ⚠️ 端口 {port_name} 访问异常: {e}")
                    
        except Exception as e:
            print(f"⚠️ smallpart端口访问异常: {e}")
    
    if not electrical_ports:
        print("🔍 未发现电学端口，跳过走线")
        return smallpart_component
    
    # 按Y坐标排序电学端口
    electrical_ports.sort(key=lambda x: x[1].center[1])
    
    # 计算smallpart边界
    bbox = main_ref.bbox()
    center_y = (bbox.bottom + bbox.top) / 2
    
    # 分割端口：上半部分和下半部分
    upper_ports = [port for name, port in electrical_ports if port.center[1] >= center_y]
    lower_ports = [port for name, port in electrical_ports if port.center[1] < center_y]
    
    print(f"📍 端口分配: {len(upper_ports)} 个上方端口, {len(lower_ports)} 个下方端口")
    
    # 创建pads并使用route_bundle连接
    routing_layer = routing_config.get('routing_layer', 'METAL')
    pad_distance = routing_config.get('pad_distance', 300)
    
    # 处理上方端口
    if upper_ports:
        # 创建上方pad端口
        upper_pad_ports = []
        num_upper = len(upper_ports)
        pad_spacing = routing_config.get('pad_spacing', 200)
        total_width = (num_upper - 1) * pad_spacing if num_upper > 1 else 0
        start_x = (bbox.left + bbox.right) / 2 - total_width / 2
        pad_y = bbox.top + pad_distance
        
        for i in range(num_upper):
            x_pos = start_x + i * pad_spacing
            pad_port = gf.Port(
                name=f"upper_pad_{i}",
                center=(x_pos, pad_y),
                width=10,
                orientation=270,  # 朝下
                layer=41  # 使用METAL layer
            )
            upper_pad_ports.append(pad_port)
            
            # 创建实际的pad (使用rectangle替代pad组件)
            pad_size = routing_config.get('pad_size', [100, 100])
            pad = gf.components.rectangle(size=pad_size, layer=41)
            pad_ref = c.add_ref(pad)
            pad_ref.move((x_pos, pad_y))
        
        # 使用route_bundle连接上方端口
        try:
            print("🔌 使用route_bundle连接上方端口...")
            
            # 转换smallpart端口为GDSFactory端口格式
            gf_upper_ports = []
            for i, port in enumerate(upper_ports):
                gf_port = gf.Port(
                    name=f"upper_src_{i}",
                    center=port.center,
                    width=10,
                    orientation=90,  # 朝上
                    layer=41  # 使用METAL layer
                )
                gf_upper_ports.append(gf_port)
            
            # 使用route_bundle连接
            routes = gf.routing.route_bundle(
                c,
                ports1=gf_upper_ports,
                ports2=upper_pad_ports,
                radius=20,
                sort_ports=True,
                cross_section=gf.cross_section(width=10, layer=41),
                separation=20
            )
            print(f"  ✅ 成功连接 {len(upper_ports)} 个上方端口")
            
        except Exception as e:
            print(f"  ❌ 上方端口连接失败: {e}")
    
    # 处理下方端口
    if lower_ports:
        # 创建下方pad端口
        lower_pad_ports = []
        num_lower = len(lower_ports)
        pad_spacing = routing_config.get('pad_spacing', 200)
        total_width = (num_lower - 1) * pad_spacing if num_lower > 1 else 0
        start_x = (bbox.left + bbox.right) / 2 - total_width / 2
        pad_y = bbox.bottom - pad_distance
        
        for i in range(num_lower):
            x_pos = start_x + i * pad_spacing
            pad_port = gf.Port(
                name=f"lower_pad_{i}",
                center=(x_pos, pad_y),
                width=10,
                orientation=90,  # 朝上
                layer=41  # 使用METAL layer
            )
            lower_pad_ports.append(pad_port)
            
            # 创建实际的pad (使用rectangle替代pad组件)
            pad_size = routing_config.get('pad_size', [100, 100])
            pad = gf.components.rectangle(size=pad_size, layer=41)
            pad_ref = c.add_ref(pad)
            pad_ref.move((x_pos, pad_y))
        
        # 使用route_bundle连接下方端口
        try:
            print("🔌 使用route_bundle连接下方端口...")
            
            # 转换smallpart端口为GDSFactory端口格式
            gf_lower_ports = []
            for i, port in enumerate(lower_ports):
                gf_port = gf.Port(
                    name=f"lower_src_{i}",
                    center=port.center,
                    width=10,
                    orientation=270,  # 朝下
                    layer=41  # 使用METAL layer
                )
                gf_lower_ports.append(gf_port)
            
            # 使用route_bundle连接
            routes = gf.routing.route_bundle(
                c,
                ports1=gf_lower_ports,
                ports2=lower_pad_ports,
                radius=20,
                sort_ports=True,
                cross_section=gf.cross_section(width=10, layer=41),
                separation=20
            )
            print(f"  ✅ 成功连接 {len(lower_ports)} 个下方端口")
            
        except Exception as e:
            print(f"  ❌ 下方端口连接失败: {e}")
    
    print(f"✅ 完成电极走线: {len(upper_ports)} 个上方端口, {len(lower_ports)} 个下方端口")
    
    return c
