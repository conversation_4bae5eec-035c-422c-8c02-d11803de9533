#!/usr/bin/env python3
"""测试组件端口"""

import gdsfactory as gf
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入PDK
from pdk import PDK, LAYERS

def test_component_ports():
    """测试组件端口"""
    
    print("🔍 测试组件端口...")
    
    # 直接导入组件
    from components.ring_with_electrode import ring_with_electrode_with_heater_with_tip, thermal_heater_ring
    
    # 测试ring_with_electrode_with_heater_with_tip组件
    comp = ring_with_electrode_with_heater_with_tip()
    
    print(f"📍 组件名: {comp.name}")
    print(f"📍 端口类型: {type(comp.ports)}")
    
    # 获取端口字典，兼容不同的GDSFactory版本
    try:
        if hasattr(comp.ports, 'keys'):
            # 老版本或其他类型
            port_keys = list(comp.ports.keys())
        else:
            # 新版本，可能是DPorts类型
            port_keys = list(comp.ports)
        
        print(f"📍 端口列表: {port_keys}")
        print(f"📍 端口数量: {len(comp.ports)}")
        
        # 打印端口详细信息
        print("\n端口详细信息:")
        for port_name in port_keys:
            port = comp.ports[port_name]
            # 获取端口属性，兼容性处理
            try:
                position = getattr(port, 'dcenter', getattr(port, 'center', 'unknown'))
                orientation = getattr(port, 'orientation', getattr(port, 'angle', 'unknown'))
                port_type = getattr(port, 'port_type', 'unknown')
                print(f"  🔌 端口 {port_name}: 位置 {position}, 朝向 {orientation}°, 类型 {port_type}")
            except Exception as e:
                print(f"  🔌 端口 {port_name}: 获取信息失败 - {e}")
    except Exception as e:
        print(f"❌ 端口操作失败: {e}")
        print(f"📍 端口对象方法: {dir(comp.ports)}")
    
    # 用正确的方法访问端口
    print("\n🎯 端口名称映射分析:")
    all_ports = comp.ports.get_all_named()
    print("实际端口名称:", list(all_ports.keys()))
    print("电学端口:", [p for p in all_ports.keys() if p.startswith('e')])
    print("光学端口:", [p for p in all_ports.keys() if p.startswith('o')])
    
    # 打印端口详细信息（正确方法）
    print("\n端口详细信息（正确方法）:")
    for port_name, port in all_ports.items():
        try:
            port_type = getattr(port, 'port_type', 'unknown')
            center = getattr(port, 'center', getattr(port, 'dcenter', 'unknown'))
            print(f"  🔌 端口 {port_name}: 类型 {port_type}, 位置 {center}")
        except Exception as e:
            print(f"  🔌 端口 {port_name}: 获取信息失败 - {e}")
    
    # 测试单独创建的thermal_heater_ring
    heater = thermal_heater_ring()
    print(f"\n🔍 thermal_heater_ring 端口: {list(heater.ports.get_all_named().keys())}")
    for port_name, port in heater.ports.get_all_named().items():
        port_type = port.port_type if hasattr(port, 'port_type') else 'unknown'
        print(f"  {port_name}: {port_type} at {port.center}")

if __name__ == "__main__":
    test_component_ports()
