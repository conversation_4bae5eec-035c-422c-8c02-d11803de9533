"""芯片级切割标记组件 - 用于在芯片间隙和边界放置切割标记"""

import gdsfactory as gf
import numpy as np
from gdsfactory.component import Component
from gdsfactory.typings import LayerSpec
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from pdk.layers import LAYER
except ImportError:
    # 如果PDK未找到，则定义虚拟图层
    print("警告: 无法从pdk.layers导入LAYER。将使用默认的虚拟图层。")
    class DummyLAYER:
        DICING = (5, 0)
    LAYER = DummyLAYER()


@gf.cell
def dicing_rectangle_mark(
    width: float = 20.0,
    height: float = 50.0,
    layer: LayerSpec = LAYER.DICING,
) -> Component:
    """创建一个矩形切割标记。

    Args:
        width: 矩形宽度 (µm)
        height: 矩形高度 (µm) 
        layer: 切割标记所在的GDS图层

    Returns:
        包含矩形切割标记的组件
    """
    c = gf.Component("dicing_rectangle_mark")
    
    # 创建矩形标记
    rect = gf.components.rectangle(
        size=(width, height),
        layer=layer,
        centered=True
    )
    c.add_ref(rect)
    
    return c


def calculate_internal_cutting_lines(smallpart_positions, grid_shape):
    """计算内部切割线位置（相邻芯片间隙中心）
    
    Args:
        smallpart_positions: smallpart的物理坐标列表 [(xmin, xmax, ymin, ymax), ...]
        grid_shape: 网格形状 (rows, cols)
        
    Returns:
        tuple: (vertical_lines, horizontal_lines) 垂直和水平切割线的x,y坐标列表
    """
    rows, cols = grid_shape
    vertical_lines = []
    horizontal_lines = []

    if not smallpart_positions or len(smallpart_positions) != rows * cols:
        print(f"警告: smallpart_positions 数量 ({len(smallpart_positions)}) 与 grid_shape ({rows}x{cols}) 不匹配。")
        return [], []

    def get_pos(r, c):
        idx = r * cols + c
        return smallpart_positions[idx]

    # 计算垂直切割线
    for c in range(cols - 1):
        max_xmax_current_col = -np.inf
        min_xmin_next_col = np.inf
        for r in range(rows):
            current_pos = get_pos(r, c)
            next_pos = get_pos(r, c + 1)
            max_xmax_current_col = max(max_xmax_current_col, current_pos[1]) # xmax
            min_xmin_next_col = min(min_xmin_next_col, next_pos[0]) # xmin
        
        center_x = (max_xmax_current_col + min_xmin_next_col) / 2
        vertical_lines.append(center_x)

    # 计算水平切割线
    for r in range(rows - 1):
        min_ymin_current_row = np.inf
        max_ymax_next_row = -np.inf
        for c in range(cols):
            current_pos = get_pos(r, c)
            next_pos = get_pos(r + 1, c)
            min_ymin_current_row = min(min_ymin_current_row, current_pos[2]) # ymin
            max_ymax_next_row = max(max_ymax_next_row, next_pos[3]) # ymax

        center_y = (min_ymin_current_row + max_ymax_next_row) / 2
        horizontal_lines.append(center_y)

    return vertical_lines, horizontal_lines


def calculate_boundary_cutting_lines(smallpart_positions, boundary_xoffset=200.0, boundary_yoffset=200.0):
    """计算边界切割线位置（阵列边界外的固定偏移距离）
    
    Args:
        smallpart_positions: smallpart的物理坐标列表 [(xmin, xmax, ymin, ymax), ...]
        boundary_xoffset: 边界偏移距离 (µm)
        boundary_yoffset: 边界偏移距离 (µm)
        
    Returns:
        tuple: (boundary_vertical_lines, boundary_horizontal_lines) 边界切割线坐标
    """
    if not smallpart_positions:
        return [], []

    all_xmin = min(pos[0] for pos in smallpart_positions)
    all_xmax = max(pos[1] for pos in smallpart_positions)
    all_ymin = min(pos[2] for pos in smallpart_positions)
    all_ymax = max(pos[3] for pos in smallpart_positions)

    boundary_vertical_lines = [all_xmin - boundary_xoffset, all_xmax + boundary_xoffset]
    boundary_horizontal_lines = [all_ymin - boundary_yoffset, all_ymax + boundary_yoffset]

    return boundary_vertical_lines, boundary_horizontal_lines


def place_dicing_marks_at_intersections(vertical_lines, horizontal_lines, mark_component):
    """在切割线交叉点放置切割标记
    
    Args:
        vertical_lines: 垂直切割线x坐标列表
        horizontal_lines: 水平切割线y坐标列表
        mark_component: 切割标记组件
        
    Returns:
        Component: 包含所有切割标记的组件
    """
    c = gf.Component("dicing_marks_grid")
    
    # 在每个交叉点放置标记
    for x in vertical_lines:
        for y in horizontal_lines:
            mark_ref = c.add_ref(mark_component)
            mark_ref.dcenter = (x, y)
    
    return c


@gf.cell
def chip_dicing_grid(
    smallpart_positions: list,
    grid_shape: tuple,
    mark_width: float = 20.0,
    mark_height: float = 50.0,
    boundary_xoffset: float = 200.0,
    boundary_yoffset: float = 200.0,
    layer: LayerSpec = LAYER.DICING,
) -> Component:
    """创建完整的芯片切割网格系统

    Args:
        smallpart_positions: smallpart的物理坐标列表 [(xmin, xmax, ymin, ymax), ...]
        grid_shape: 网格形状 (rows, cols)
        mark_width: 切割标记宽度 (µm)
        mark_height: 切割标记高度 (µm)
        boundary_offset: 边界偏移距离 (µm)
        layer: 切割标记图层
        
    Returns:
        Component: 包含完整切割标记网格的组件
    """
    c = gf.Component("chip_dicing_grid")
    
    # 创建切割标记组件
    mark = dicing_rectangle_mark(
        width=mark_width,
        height=mark_height,
        layer=layer
    )
    
    # 计算内部切割线位置
    internal_vertical, internal_horizontal = calculate_internal_cutting_lines(
        smallpart_positions, grid_shape
    )
    
    # 计算边界切割线位置
    boundary_vertical, boundary_horizontal = calculate_boundary_cutting_lines(
        smallpart_positions, boundary_xoffset, boundary_yoffset
    )
    
    # 合并所有切割线
    all_vertical = internal_vertical + boundary_vertical
    all_horizontal = internal_horizontal + boundary_horizontal
    
    # 在交叉点放置切割标记
    if all_vertical and all_horizontal:
        marks_grid = place_dicing_marks_at_intersections(
            all_vertical, all_horizontal, mark
        )
        c.add_ref(marks_grid)
    
    return c


if __name__ == "__main__":
    # 测试单个切割标记
    mark = dicing_rectangle_mark()
    mark.show()
    
    # 测试切割网格（需要实际的grid_refs数据）
    print("切割标记组件创建完成！")
