{"cells": [{"cell_type": "code", "execution_count": 1, "id": "86a7a1ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-101.35000000000001 -120.0 -120.0 120.0 -120.60000000000001\n", "\u001b[32m2025-08-17 11:57:43.986\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3954\u001b[0m - \u001b[1mklive v0.3.3: Reloaded file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\722661919.oas'\u001b[0m\n", "\u001b[32m2025-08-17 11:57:43.986\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3954\u001b[0m - \u001b[1mklive v0.3.3: Reloaded file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\722661919.oas'\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center             </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.6   │ 180.0       │ WG (1/0) │ (-153.425, 96.007) │ optical   │\n", "│ o2   │ 0.6   │ 0.0         │ WG (1/0) │ (153.425, 96.007)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter            \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.6   │ 180.0       │ WG (1/0) │ (-153.425, 96.007) │ optical   │\n", "│ o2   │ 0.6   │ 0.0         │ WG (1/0) │ (153.425, 96.007)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-08-17 12:56:19.375\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\3073659641.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:13:10.948\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\4166875789.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:13:10.948\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\4166875789.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:41:30.982\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\4166875789.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:41:30.982\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\4166875789.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:48:17.496\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\1312191993.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:48:17.496\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\1312191993.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:51:12.473\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2956075898.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:51:12.473\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2956075898.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:53:54.727\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2146770148.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:53:54.727\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2146770148.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:54:20.967\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\246224893.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:54:20.967\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\246224893.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:54:25.626\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2008296018.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:54:25.626\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2008296018.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:54:43.957\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\1503362513.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:54:43.957\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\1503362513.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:58:06.742\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\3396423625.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:58:06.742\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\3396423625.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:58:17.240\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\1972636117.oas'\u001b[0m\n", "\u001b[32m2025-08-17 13:58:17.240\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\1972636117.oas'\u001b[0m\n"]}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "@gf.cell\n", "def create_ring_electrode_with_teeth(\n", "    center_x: float = 0,           # 圆心X坐标\n", "    center_y: float = 0,           # 圆心Y坐标\n", "    inner_radius: float = 117,      # 内圆半径（齿的内端）\n", "    outer_radius: float = 120,     # 外圆半径（环的外边缘）  \n", "    teeth_count: int = 186,         # 齿的数量\n", "    teeth_angle_fraction: float = 0.2,      # 每个齿的角度宽度（度）\n", "    teeth_length:float =22,\n", "    layer: tuple = (4, 0)          # 金属层\n", "):\n", "    \"\"\"\n", "    创建带有齿状结构的圆环电极\n", "    \n", "    Args:\n", "        center_x, center_y: 圆心坐标\n", "        inner_radius: 内圆半径（齿的内端半径）\n", "        outer_radius: 外圆半径（环的外边缘半径）\n", "        teeth_count: 齿的数量\n", "        teeth_angle: 每个齿的角度宽度（度）\n", "        layer: 层\n", "        \n", "    注意：齿从inner_radius延伸，环从齿的外端到outer_radius\n", "    \"\"\"\n", "    c = gf.Component()\n", "    teeth_angle=teeth_angle_fraction*360/teeth_count\n", "    # 计算齿的外端半径（环的内边缘）\n", "    # 假设齿占总半径差的70%，环占30%\n", "\n", "    \n", "    # 1. 创建基础圆环（从齿的外端到外圆）\n", "    ring_width = outer_radius - inner_radius\n", "    ring = gf.components.ring(\n", "        radius=inner_radius + ring_width/2,  # 环的中心半径\n", "        width=ring_width,\n", "        layer=layer\n", "    )\n", "    ring_ref = c << ring\n", "    ring_ref.dmove([center_x, center_y])\n", "    \n", "    # 2. 计算齿的角度分布\n", "    teeth_angles = np.linspace(0, 2*np.pi, teeth_count, endpoint=False)\n", "    teeth_angle_rad = np.deg2rad(teeth_angle)\n", "    \n", "    # 3. 为每个齿创建扇形\n", "    for angle in teeth_angles:\n", "        # 创建扇形齿（从圆心扫出）\n", "        # 扇形的起始和结束角度\n", "        start_angle = angle - teeth_angle_rad/2\n", "        end_angle = angle + teeth_angle_rad/2\n", "        \n", "        # 创建扇形的点\n", "        n_points = 20  # 扇形弧的点数\n", "        arc_angles = np.linspace(start_angle, end_angle, n_points)\n", "        \n", "        # 构建扇形的所有点\n", "        points = []\n", "        \n", "        # 内弧点（从起始角到结束角）\n", "        for a in arc_angles:\n", "            x = center_x + inner_radius * np.cos(a)\n", "            y = center_y + inner_radius * np.sin(a)\n", "            points.append([x, y])\n", "            \n", "        # 外弧点（从结束角到起始角，反向）\n", "        for a in reversed(arc_angles):\n", "            x = center_x + (inner_radius-teeth_length) * np.cos(a)\n", "            y = center_y + (inner_radius-teeth_length) * np.sin(a)\n", "            points.append([x, y])\n", "        \n", "        # 创建齿的多边形\n", "        c.add_polygon(points, layer=layer)\n", "    \n", "    # 4. 在圆环正上方添加圆形pad\n", "    pad_radius = 50\n", "    pad_distance = 10\n", "    pad_center_y = center_y + outer_radius + pad_distance+pad_radius\n", "    \n", "    # 创建圆形pad\n", "    pad = gf.components.circle(radius=pad_radius, layer=layer)\n", "    pad_ref = c << pad\n", "    pad_ref.dmove([center_x, pad_center_y])\n", "    \n", "    # 5. 用多边形连接圆环和pad\n", "    # 圆环上的连接点（正上方几个点）\n", "    ring_points = []\n", "    for i in range(-1, 2):  # 3个点：左、中、右\n", "        angle = np.pi/2 + i * 0.1/outer_radius*pad_radius  # 90度附近的3个点\n", "        x = center_x + outer_radius * np.cos(angle)\n", "        y = center_y + outer_radius * np.sin(angle)\n", "        ring_points.append([x, y])\n", "    \n", "    # pad底部的连接点（对应的几个点）\n", "    pad_points = []\n", "    for i in range(-1, 2):  # 3个点\n", "        angle = -np.pi/2 - i * 0.1  # pad底部对应点\n", "        x = center_x + pad_radius * np.cos(angle)\n", "        y = pad_center_y + pad_radius * np.sin(angle)\n", "        pad_points.append([x, y])\n", "    \n", "    # 创建连接多边形\n", "    connection_points = ring_points + list(reversed(pad_points))\n", "    c.add_polygon(connection_points, layer=layer)\n", "    \n", "    c.flatten()\n", "    return c\n", "\n", "# 演示不同参数的电极\n", "# dense_electrode = create_ring_electrode_with_teeth()\n", "# dense_electrode.plot()\n", "# dense_electrode.show()\n", "@gf.cell\n", "def ring_with_electrode(ring_width:float=1.5,\n", "                        waveguide_width:float=0.6,\n", "                        gap:float=0.6,\n", "                        coupling_angle_coverage:float=10,\n", "                        ring_radius:float=100-1.5/2\n", "                        ):\n", "    section_inner = gf.cross_section.cross_section(\n", "        width=ring_width,\n", "        offset=0,\n", "        layer=(1, 0)\n", "    )\n", "    section_outer = gf.cross_section.cross_section(\n", "        width=waveguide_width,\n", "        offset=0,\n", "        layer=(1, 0)\n", "    )\n", "\n", "    # 创建基础环形耦合器\n", "    coupler = gf.components.ring_single_bend_coupler(\n", "        radius=ring_radius, gap=gap, \n", "        coupling_angle_coverage=coupling_angle_coverage, bend='bend_circular', \n", "        bend_output='bend_euler_all_angle', \n", "        length_x=0, length_y=0, \n", "        cross_section_inner=section_inner, \n", "        cross_section_outer=section_outer)\n", "\n", "    # 创建新的组件\n", "    c = gf.Component()\n", "\n", "    # 添加耦合器\n", "    coupler_ref = c << coupler\n", "    electrode_ref = c << create_ring_electrode_with_teeth()\n", "    coupler_ref.dcenter=(0,0)\n", "    y=coupler_ref.ymin\n", "    y1=electrode_ref.ymin\n", "    x1=electrode_ref.xmin\n", "    x2=electrode_ref.xmax\n", "    electrode_ref.ymin=y+ring_width/2+ring_radius-(x2-x1)/2\n", "    # # 添加两个直波导\n", "    s1 = c << gf.get_component(\"straight\", length=100, cross_section=section_outer)\n", "    s2 = c << gf.get_component(\"straight\", length=100, cross_section=section_outer)\n", "\n", "    # # 连接直波导到耦合器的端口\n", "    s1.connect(\"o1\", coupler_ref.ports[\"o1\"])\n", "    s2.connect(\"o1\", coupler_ref.ports[\"o2\"])\n", "\n", "\n", "    # 添加端口（新的端口位置）\n", "    c.add_port(\"o1\", port=s1.ports[\"o2\"])\n", "    # c.add_port(\"o2\", port=coupler_ref.ports[\"o2\"])\n", "    # c.add_port(\"o3\", port=coupler_ref.ports[\"o1\"])\n", "    c.add_port(\"o2\", port=s2.ports[\"o2\"])\n", "    return c\n", "#c = gf.components.extend_ports(component=c, length=50, port_type='optical', centered=False, allow_width_mismatch=False, auto_taper=True).copy()\n", "c=ring_with_electrode()\n", "c.draw_ports()\n", "c.plot()\n", "c.show()\n", "c.pprint_ports()"]}, {"cell_type": "code", "execution_count": 4, "id": "2ecb37dc", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "\n", "c = gf.components.ring_double_heater(gap=0.2, length_x=1, length_y=0.01, coupler_ring='coupler_ring', straight='straight', bend='bend_euler', cross_section_heater='heater_metal', cross_section_waveguide_heater='strip_heater_metal', cross_section='strip', via_stack='via_stack_heater_mtop_mini', via_stack_offset=(1, 0), with_drop=True).copy()\n", "c.draw_ports()\n", "c.plot()\n", "c.show()"]}, {"cell_type": "code", "execution_count": 38, "id": "d3421bce", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "section_outer = gf.cross_section.cross_section(\n", "    width=0.6,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "c1=gf.Component()\n", "s=c1<<gf.get_component(\"straight\", length=10, cross_section=section_outer)\n", "\n", "c2 = gf.components.extend_ports(component=c1, length=5000, port_type='optical', centered=False, allow_width_mismatch=False, auto_taper=True).copy()\n", "c2.draw_ports()\n", "c1.plot()\n", "c2.show()"]}, {"cell_type": "code", "execution_count": 19, "id": "6bd5f0d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 内置欧拉弯曲 ===\n", "内置欧拉弯曲端口信息:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173: UserWarning: bend_euler angle should be 90 or 180. Got 45. Use bend_euler_all_angle instead.\n", "  v = func(*args, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                       </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                   │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (14.142, 5.8580000000000005) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴──────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                   │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (14.142, 5.8580000000000005) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴──────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 路径欧拉弯曲 ===\n", "路径欧拉弯曲端口信息:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                                  </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                              │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (20.928068031846067, 8.668689613057447) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (0.0, 0.0)                              │ optical   │\n", "│ o2   │ 0.5   │ 45.0        │ WG (1/0) │ (20.928068031846067, 8.668689613057447) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴─────────────────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "\n", "def custom_euler_bend(\n", "    angle: float = 90,  # 最终角度（度）\n", "    radius: float = 10,  # 最终曲率半径\n", "    length: float = 50,  # 弯曲段长度\n", "    width: float = 0.5,  # 波导宽度\n", "    layer: tuple = (1, 0),  # 层\n", "    n_points: int = 100  # 路径点数\n", "):\n", "    \"\"\"\n", "    创建自定义欧拉弯曲波导\n", "    - 传播方向：从左往右\n", "    - 曲率：使用欧拉螺旋，从0平滑变化到1/radius\n", "    - 最终输出特定角度的波导\n", "    \"\"\"\n", "    c = gf.Component()\n", "    \n", "    # 转换角度为弧度\n", "    angle_rad = np.radians(angle)\n", "    \n", "    # 创建路径参数\n", "    t = np.linspace(0, 1, n_points)\n", "    \n", "    # 起始点在原点，方向向右（0度）\n", "    x0, y0 = 0, 0\n", "    \n", "    # 欧拉螺旋的曲率变化：线性从0变化到最大曲率\n", "    max_curvature = 1.0 / radius\n", "    curvature = max_curvature * t  # 线性变化\n", "    \n", "    # 计算弧长参数\n", "    ds = length / (n_points - 1)\n", "    \n", "    # 使用欧拉螺旋公式计算角度（曲率的积分）\n", "    angles = np.zeros(n_points)\n", "    for i in range(1, n_points):\n", "        # 欧拉螺旋：角度 = ∫κ(s)ds，其中κ(s) = κ_max * s/L\n", "        # 积分结果：θ = κ_max * s²/(2*L)\n", "        s = i * ds\n", "        angles[i] = max_curvature * s**2 / (2 * length)\n", "    \n", "    # 缩放角度以达到目标最终角度\n", "    if angles[-1] != 0:\n", "        scale_factor = angle_rad / angles[-1]\n", "        angles = angles * scale_factor\n", "    \n", "    # 计算路径点坐标\n", "    x = np.zeros(n_points)\n", "    y = np.zeros(n_points)\n", "    x[0] = x0\n", "    y[0] = y0\n", "    \n", "    for i in range(1, n_points):\n", "        # 当前方向角度\n", "        current_angle = angles[i]\n", "        # 步长在当前方向上的分量\n", "        x[i] = x[i-1] + ds * np.cos(current_angle)\n", "        y[i] = y[i-1] + ds * np.sin(current_angle)\n", "    \n", "    # 创建路径点\n", "    points = list(zip(x, y))\n", "    \n", "    # 使用gdsfactory的路径功能创建平滑路径\n", "    path = gf.path.smooth(points, radius=0.5)\n", "    \n", "    # 创建截面\n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    # 创建波导\n", "    waveguide = path.extrude(cross_section)\n", "    c.add_ref(waveguide)\n", "    \n", "    # 添加端口\n", "    # 起始端口（左向右，输入端）\n", "    c.add_port(\n", "        name=\"o1\",\n", "        center=(x[0], y[0]),\n", "        width=width,\n", "        orientation=180,  # 向右传播，端口朝左\n", "        layer=layer\n", "    )\n", "    \n", "    # 结束端口（输出端）\n", "    c.add_port(\n", "        name=\"o2\", \n", "        center=(x[-1], y[-1]),\n", "        width=width,\n", "        orientation=np.degrees(angles[-1]),  # 最终角度方向\n", "        layer=layer\n", "    )\n", "    \n", "    return c\n", "\n", "# 更简单的方法：直接使用gdsfactory的内置欧拉弯曲\n", "def simple_euler_bend(\n", "    angle: float = 90,\n", "    radius: float = 10,\n", "    width: float = 0.5,\n", "    layer: tuple = (1, 0)\n", "):\n", "    \"\"\"\n", "    使用gdsfactory内置的欧拉弯曲\n", "    \"\"\"\n", "    # 创建截面\n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    # 创建欧拉弯曲\n", "    bend = gf.components.bend_euler(\n", "        angle=angle,\n", "        radius=radius,\n", "        cross_section=cross_section\n", "    )\n", "    \n", "    return bend\n", "\n", "# 最简单的方法：使用路径创建欧拉弯曲\n", "def path_euler_bend(\n", "    angle: float = 90,\n", "    radius: float = 10,\n", "    width: float = 0.5,\n", "    layer: tuple = (1, 0)\n", "):\n", "    \"\"\"\n", "    使用路径方法创建欧拉弯曲\n", "    \"\"\"\n", "    c = gf.Component()\n", "    \n", "    # 创建欧拉路径\n", "    path = gf.path.euler(radius=radius, angle=angle)\n", "    \n", "    # 创建截面\n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    # 挤出波导\n", "    waveguide_ref = c << path.extrude(cross_section)\n", "    \n", "    # 添加端口\n", "    c.add_ports(waveguide_ref.ports)\n", "    \n", "    return c\n", "\n", "# 测试不同的欧拉弯曲方法\n", "print(\"=== 内置欧拉弯曲 ===\")\n", "builtin_bend = simple_euler_bend(\n", "    angle=45,\n", "    radius=20,\n", "    width=0.5,\n", "    layer=(1, 0)\n", ")\n", "\n", "print(\"内置欧拉弯曲端口信息:\")\n", "builtin_bend.pprint_ports()\n", "builtin_bend.plot()\n", "\n", "print(\"\\n=== 路径欧拉弯曲 ===\")\n", "path_bend = path_euler_bend(\n", "    angle=45,\n", "    radius=20,\n", "    width=0.5,\n", "    layer=(1, 0)\n", ")\n", "\n", "print(\"路径欧拉弯曲端口信息:\")\n", "path_bend.pprint_ports()\n", "path_bend.plot()\n", "\n", "# 显示组件\n", "builtin_bend.show()\n", "path_bend.show()"]}, {"cell_type": "code", "execution_count": 9, "id": "11535304", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c = gf.Component()\n", "# 这里是波导（1，0）\n", "#WG = gf.components.bends.bend_euler_s(layer=(1,0))\n", "#WG=bend_s_offset(offset=offset, radius=radius)\n", "#WG=gf.components.crossing_etched(width=0.8, r1=r1, r2=r2, w=w, L=3.5, layer_wg='WG')\n", "#xs = gf.cross_section.strip(width=1, layer=(1, 0))\n", "# WG=gf.components.straight(\n", "# length=10,  # Length in microns\n", "# cross_section=xs\n", "# )       \n", "section_inner = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "section_outer = gf.cross_section.cross_section(\n", "    width=0.6,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "\n", "# 创建基础环形耦合器\n", "coupler = gf.components.coupler_ring_bend(\n", "    coupler_gap=0.5,\n", "    radius=50,\n", "    coupling_angle_coverage=20,\n", "    length_x=0,\n", "    cross_section_inner=section_inner,\n", "    cross_section_outer=section_outer,\n", "    bend_output=\"bend_euler_all_angle\",\n", "    straight=\"straight\",\n", ")\n", "\n", "# 创建新的组件\n", "WG = gf.Component()\n", "\n", "# 添加耦合器\n", "coupler_ref = WG << coupler\n", "\n", "# 添加两个直波导\n", "s1 = WG << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "s2 = WG << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "\n", "# 连接直波导到耦合器的端口\n", "s1.connect(\"o1\", coupler_ref.ports[\"o1\"])\n", "s2.connect(\"o1\", coupler_ref.ports[\"o4\"])\n", "\n", "# 添加端口（新的端口位置）\n", "WG.add_port(\"o1\", port=s1.ports[\"o2\"])\n", "# WG.add_port(\"o2\", port=coupler_ref.ports[\"o2\"])\n", "WG.add_port(\"o3\", port=coupler_ref.ports[\"o3\"])\n", "# WG.add_port(\"o4\", port=s2.ports[\"o2\"])\n", "# Add the MMI component as a reference to our main component `c`.\n", "WG_ref = c.add_ref(WG)\n", "# Create a rectangle background with the same size as the MMI.\n", "# The .size attribute is a convenient way to get the (width, height) tuple.\n", "box = gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(3,0)\n", ")\n", "slab= gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(2,0)\n", ")\n", "clad=gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(5,0)\n", ")\n", "# Add the background rectangle as a reference to `c`.\n", "rect_ref = c.add_ref(box)\n", "slab_ref = c.add_ref(slab) \n", "clad_ref = c.add_ref(clad)\n", "# Align the center of the rectangle with the center of the MMI.\n", "WG_ref.center = (0, 0)\n", "rect_ref.center = WG_ref.center\n", "slab_ref.center = WG_ref.center\n", "clad_ref.center = WG_ref.center\n", "# Add the optical ports from the MMI reference to the main component `c`.\n", "c.add_ports(WG_ref.ports)\n", "#c.add_port('o1',port=WG_ref.ports['o1'])\n", "#c.add_port('o2',port=WG_ref.ports['o3'])\n", "c.draw_ports()\n", "c.plot()\n", "c.show()"]}, {"cell_type": "code", "execution_count": 10, "id": "447b337e", "metadata": {}, "outputs": [], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "from gdsfactory.component import Component\n", "from gdsfactory.typings import CrossSectionSpec, AnyComponentFactory, ComponentSpec\n", "\n", "# bend_circular_all_angle 是一个示例函数，实际使用时请替换为您自己的\n", "# 这里我们定义一个简单的版本以保证代码可运行\n", "@gf.cell\n", "def bend_circular_all_angle(**kwargs):\n", "    return gf.components.bend_circular(**kwargs)\n", "\n", "# ==============================================================================\n", "# 步骤1: 修正 gvc_bend_optimal 函数，使其端口角度绝对精确\n", "# ==============================================================================\n", "@gf.cell\n", "def gvc_bend_optimal(\n", "    final_angle: float = 45.0,\n", "    final_curvature: float = -0.1,\n", "    peak_curvature: float = 0.2,\n", "    cross_section: gf.typings.CrossSectionSpec = \"strip\",\n", "    npoints: int = 720,\n", ") -> gf.Component:\n", "    \"\"\"\n", "    创建一个理论最优的通用可变曲率弯曲 (C2连续)。\n", "\n", "    该弯曲的曲率 k(s) 及其一阶导数 dk/ds 全程连续，\n", "    从而最大限度地减少过渡损耗。端口角度被强制设为精确值，\n", "    以避免 \"off-grid\" 错误。\n", "\n", "    Args:\n", "        final_angle: 最终的目标角度 (degrees)。\n", "        final_curvature: 最终的目标曲率 (1/μm)。正=逆时针, 负=顺时针。\n", "        peak_curvature: 路径中的峰值曲率 (1/μm)。这是一个正值，\n", "                        用作损耗/尺寸的控制参数。值越小，弯曲越平缓，\n", "                        损耗越低，但尺寸越大。\n", "        cross_section: 波导的横截面。\n", "        npoints: 离散化点数。\n", "    \"\"\"\n", "    # --- 输入参数验证 ---\n", "    k_final = final_curvature\n", "    k_peak = peak_curvature\n", "    \n", "    if k_peak <= 0:\n", "        raise ValueError(\"peak_curvature 必须为正数。\")\n", "    if np.abs(k_final) >= k_peak:\n", "        raise ValueError(\n", "            f\"peak_curvature ({k_peak}) 必须大于等于最终曲率的绝对值 \"\n", "            f\"abs(final_curvature) ({np.abs(k_final)}).\"\n", "        )\n", "\n", "    # --- 核心逻辑: 根据输入参数反解路径长度 L1 和 L2 ---\n", "    theta_final_rad = np.deg2rad(final_angle)\n", "    \n", "    denominator = (k_peak / 2) + (k_peak + k_final) / 2 * (k_peak - k_final) / k_peak\n", "    if np.isclose(denominator, 0):\n", "        denominator = 1e-9\n", "    L1 = theta_final_rad / denominator\n", "\n", "    L2 = L1 * (k_peak - k_final) / k_peak\n", "    \n", "    if L1 < 0 or L2 < 0:\n", "        raise ValueError(\n", "            \"计算出的路径长度为负，无法创建弯曲。 \"\n", "            \"请尝试增大 'peak_curvature' 或减小 'final_angle'。\"\n", "        )\n", "\n", "    # --- 构建路径 ---\n", "    s_total = np.linspace(0, L1 + L2, npoints)\n", "    k = np.zeros_like(s_total)\n", "\n", "    for i, s in enumerate(s_total):\n", "        if s <= L1:\n", "            k[i] = (k_peak / 2) * (1 - np.cos(np.pi * s / L1))\n", "        else:\n", "            s_prime = s - L1\n", "            k[i] = (k_peak + k_final) / 2 + (k_peak - k_final) / 2 * np.cos(np.pi * s_prime / L2)\n", "\n", "    theta_cum = np.zeros_like(s_total)\n", "    for i in range(1, npoints):\n", "        theta_cum[i] = theta_cum[i-1] + (k[i] + k[i-1]) * (s_total[i] - s_total[i-1]) / 2\n", "\n", "    x = np.zeros_like(s_total)\n", "    y = np.zeros_like(s_total)\n", "    for i in range(1, npoints):\n", "        ds = s_total[i] - s_total[i-1]\n", "        avg_angle = (theta_cum[i] + theta_cum[i-1]) / 2\n", "        x[i] = x[i-1] + np.cos(avg_angle) * ds\n", "        y[i] = y[i-1] + np.sin(avg_angle) * ds\n", "        \n", "    points = np.column_stack((x, y))\n", "\n", "    # --- 创建 gdsfactory 组件 (最关键的修正) ---\n", "    c = gf.Component()\n", "    \n", "    # 1. 创建一个 Path 对象并赋予其几何点\n", "    path = gf.Path()\n", "    path.points = points\n", "    \n", "    # 2. 强制设定完美的起始和结束角度，覆盖数值误差\n", "    #    假设输入总是水平向右\n", "    path.start_angle = 0.0\n", "    path.end_angle = final_angle\n", "    \n", "    # 3. 使用这个修正了角度信息的 Path 对象来创建组件\n", "    bend = c << gf.path.extrude(path, cross_section=cross_section)\n", "    c.add_ports(bend.ports)\n", "    \n", "    return c\n", "\n", "# ==============================================================================\n", "# 步骤2: 修正 coupler_bend 函数的连接逻辑\n", "# ==============================================================================\n", "@gf.cell_with_module_name\n", "def coupler_bend(\n", "    radius: float = 100.0,\n", "    coupler_gap: float = 0.2,\n", "    coupling_angle_coverage: float = 60.0,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    bend: AnyComponentFactory = bend_circular_all_angle,\n", ") -> Component:\n", "    r\"\"\"紧凑的弯曲耦合器，带有平滑的出口过渡。\n", "\n", "    Args:\n", "        radius: 内圈弯曲半径 (um).\n", "        coupler_gap: 耦合间隙 (um).\n", "        coupling_angle_coverage: 耦合区域覆盖的角度 (degrees).\n", "        cross_section_inner: 内圈波导的截面.\n", "        cross_section_outer: 外圈波导的截面.\n", "        bend: 用于构建耦合段弯曲的函数.\n", "    \"\"\"\n", "    c = Component()\n", "\n", "    xi = gf.get_cross_section(cross_section_inner)\n", "    xo = gf.get_cross_section(cross_section_outer)\n", "\n", "    angle_inner = 90\n", "    angle_outer = coupling_angle_coverage / 2\n", "    gap = coupler_gap\n", "\n", "    width = xo.width / 2 + xi.width / 2\n", "    spacing = gap + width\n", "\n", "    # --- 创建耦合段的两个弯曲 ---\n", "    bend90_inner = gf.get_component(\n", "        bend,\n", "        radius=radius,\n", "        cross_section=cross_section_inner,\n", "        angle=angle_inner,\n", "    )\n", "    bend_coupling_outer = gf.get_component(\n", "        bend,\n", "        radius=radius + spacing,\n", "        cross_section=cross_section_outer,\n", "        angle=angle_outer,\n", "    )\n", "    \n", "    # --- 创建平滑的出口过渡弯曲 ---\n", "    output_transition_bend = gvc_bend_optimal(\n", "        final_angle=angle_outer,\n", "        final_curvature=1 / (radius + spacing),\n", "        peak_curvature=1.05 / (radius + spacing),\n", "        cross_section=cross_section_outer,\n", "    )\n", "\n", "    # --- 布局和连接 (修正后的逻辑) ---\n", "    # 创建左半部分\n", "    c_left = Component()\n", "    bend_inner_ref = c_left << bend90_inner\n", "    bend_inner_ref.mirror()\n", "    \n", "    bend_coupling_outer_ref = c_left << bend_coupling_outer\n", "    bend_coupling_outer_ref.connect(\"o1\", bend_inner_ref.ports[\"o1\"], mirror=True)\n", "    \n", "    output_ref = c_left << output_transition_bend\n", "    output_ref.connect(\"o1\", bend_coupling_outer_ref.ports[\"o2\"], mirror=True)\n", "\n", "    # 添加左半部分的端口\n", "    c_left.add_port(\"o1\", port=bend_inner_ref.ports[\"o1\"])\n", "    c_left.add_port(\"o2\", port=bend_inner_ref.ports[\"o2\"])\n", "    c_left.add_port(\"o3\", port=output_ref.ports[\"o2\"])\n", "    c_left.add_port(\"o4\", port=bend_coupling_outer_ref.ports[\"o1\"])\n", "\n", "    # 创建右半部分 (镜像)\n", "    c_right = c_left.mirror()\n", "\n", "    # 将左右两部分组合到最终的组件中\n", "    left_ref = c << c_left\n", "    right_ref = c << c_right\n", "    right_ref.connect(\"o2\", left_ref.ports[\"o2\"])\n", "\n", "    # 添加最终的端口\n", "    c.add_port(\"o1\", port=left_ref.ports[\"o1\"])\n", "    c.add_port(\"o2\", port=right_ref.ports[\"o1\"])\n", "    c.add_port(\"o3\", port=right_ref.ports[\"o3\"])\n", "    c.add_port(\"o4\", port=left_ref.ports[\"o3\"])\n", "\n", "    return c\n", "\n", "if __name__ == \"__main__\":\n", "    # 创建并显示组件\n", "    c = coupler_bend()\n", "    c.show()\n"]}, {"cell_type": "code", "execution_count": 30, "id": "05b4103c", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzAAAAJoCAYAAAC5ogQ1AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAMTgAADE4Bf3eMIwAAYYlJREFUeJzt3YGxrDiWtW2qop0YP8aM349247hRfpQZ7cc1o/7I+Tpn<PERSON><PERSON>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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "\n", "def create_ring_electrode_with_teeth(\n", "    center_x: float = 0,           # 圆心X坐标\n", "    center_y: float = 0,           # 圆心Y坐标\n", "    inner_radius: float = 117,      # 内圆半径（齿的内端）\n", "    outer_radius: float = 120,     # 外圆半径（环的外边缘）  \n", "    teeth_count: int = 186,         # 齿的数量\n", "    teeth_angle_fraction: float = 0.2,      # 每个齿的角度宽度（度）\n", "    teeth_length:float =22,\n", "    layer: tuple = (4, 0)          # 金属层\n", "):\n", "    \"\"\"\n", "    创建带有齿状结构的圆环电极\n", "    \n", "    Args:\n", "        center_x, center_y: 圆心坐标\n", "        inner_radius: 内圆半径（齿的内端半径）\n", "        outer_radius: 外圆半径（环的外边缘半径）\n", "        teeth_count: 齿的数量\n", "        teeth_angle: 每个齿的角度宽度（度）\n", "        layer: 层\n", "        \n", "    注意：齿从inner_radius延伸，环从齿的外端到outer_radius\n", "    \"\"\"\n", "    c = gf.Component()\n", "    teeth_angle=teeth_angle_fraction*360/teeth_count\n", "    # 计算齿的外端半径（环的内边缘）\n", "    # 假设齿占总半径差的70%，环占30%\n", "\n", "    \n", "    # 1. 创建基础圆环（从齿的外端到外圆）\n", "    ring_width = outer_radius - inner_radius\n", "    ring = gf.components.ring(\n", "        radius=inner_radius + ring_width/2,  # 环的中心半径\n", "        width=ring_width,\n", "        layer=layer\n", "    )\n", "    ring_ref = c << ring\n", "    ring_ref.dmove([center_x, center_y])\n", "    \n", "    # 2. 计算齿的角度分布\n", "    teeth_angles = np.linspace(0, 2*np.pi, teeth_count, endpoint=False)\n", "    teeth_angle_rad = np.deg2rad(teeth_angle)\n", "    \n", "    # 3. 为每个齿创建扇形\n", "    for angle in teeth_angles:\n", "        # 创建扇形齿（从圆心扫出）\n", "        # 扇形的起始和结束角度\n", "        start_angle = angle - teeth_angle_rad/2\n", "        end_angle = angle + teeth_angle_rad/2\n", "        \n", "        # 创建扇形的点\n", "        n_points = 20  # 扇形弧的点数\n", "        arc_angles = np.linspace(start_angle, end_angle, n_points)\n", "        \n", "        # 构建扇形的所有点\n", "        points = []\n", "        \n", "        # 内弧点（从起始角到结束角）\n", "        for a in arc_angles:\n", "            x = center_x + inner_radius * np.cos(a)\n", "            y = center_y + inner_radius * np.sin(a)\n", "            points.append([x, y])\n", "            \n", "        # 外弧点（从结束角到起始角，反向）\n", "        for a in reversed(arc_angles):\n", "            x = center_x + (inner_radius-teeth_length) * np.cos(a)\n", "            y = center_y + (inner_radius-teeth_length) * np.sin(a)\n", "            points.append([x, y])\n", "        \n", "        # 创建齿的多边形\n", "        c.add_polygon(points, layer=layer)\n", "    \n", "    # 4. 在圆环正上方添加圆形pad\n", "    pad_radius = 50\n", "    pad_distance = 10\n", "    pad_center_y = center_y + outer_radius + pad_distance+pad_radius\n", "    \n", "    # 创建圆形pad\n", "    pad = gf.components.circle(radius=pad_radius, layer=layer)\n", "    pad_ref = c << pad\n", "    pad_ref.dmove([center_x, pad_center_y])\n", "    \n", "    # 5. 用多边形连接圆环和pad\n", "    # 圆环上的连接点（正上方几个点）\n", "    ring_points = []\n", "    for i in range(-1, 2):  # 3个点：左、中、右\n", "        angle = np.pi/2 + i * 0.1/outer_radius*pad_radius  # 90度附近的3个点\n", "        x = center_x + outer_radius * np.cos(angle)\n", "        y = center_y + outer_radius * np.sin(angle)\n", "        ring_points.append([x, y])\n", "    \n", "    # pad底部的连接点（对应的几个点）\n", "    pad_points = []\n", "    for i in range(-1, 2):  # 3个点\n", "        angle = -np.pi/2 - i * 0.1  # pad底部对应点\n", "        x = center_x + pad_radius * np.cos(angle)\n", "        y = pad_center_y + pad_radius * np.sin(angle)\n", "        pad_points.append([x, y])\n", "    \n", "    # 创建连接多边形\n", "    connection_points = ring_points + list(reversed(pad_points))\n", "    c.add_polygon(connection_points, layer=layer)\n", "    \n", "    c.flatten()\n", "    return c\n", "\n", "# 演示不同参数的电极\n", "dense_electrode = create_ring_electrode_with_teeth()\n", "dense_electrode.plot()\n", "dense_electrode.show()"]}, {"cell_type": "code", "execution_count": 5, "id": "7ee8da62", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 环形耦合器分析 ===\n", "几何中心: (0.00, -101.05)\n", "组件边界: x=(-100.75, 100.75), y=(-202.40, 0.30)\n", "组件尺寸: 宽度=201.50, 高度=202.70\n", "组件信息: Info()\n", "\n", "端口信息:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o2   │ 0.6   │ 0.0         │ WG (1/0) │ (53.425000000000004, -5.043)  │ optical   │\n", "│ o1   │ 0.6   │ 180.0       │ WG (1/0) │ (-53.425000000000004, -5.043) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o2   │ 0.6   │ 0.0         │ WG (1/0) │ (53.425000000000004, -5.043)  │ optical   │\n", "│ o1   │ 0.6   │ 180.0       │ WG (1/0) │ (-53.425000000000004, -5.043) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "估计的环半径: 80.60 μm\n", "圆心标记用红色十字表示\n", "估计的环轮廓用绿色圆圈表示\n", "\n", "=== 组合组件分析 ===\n", "几何中心: (0.00, -101.05)\n", "组件边界: x=(-153.72, 153.72), y=(-202.40, 0.30)\n", "组件尺寸: 宽度=307.45, 高度=202.70\n", "组件信息: Info()\n", "\n", "=== 使用方法 ===\n", "1. 查看上面的分析结果，获得估计的圆心位置\n", "2. 如果需要调整，可以调用: test = create_ring_with_center_at(x, y, radius)\n", "3. 然后显示: test.show() 来验证圆心位置是否正确\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 确定环形耦合器的圆心位置\n", "import gdsfactory as gf\n", "import numpy as np\n", "\n", "def find_ring_center(component):\n", "    \"\"\"\n", "    确定环形耦合器的圆心位置\n", "    \"\"\"\n", "    # 方法1：通过几何中心估算\n", "    center_x = (component.xmax + component.xmin) / 2\n", "    center_y = (component.ymax + component.ymin) / 2\n", "    \n", "    print(f\"几何中心: ({center_x:.2f}, {center_y:.2f})\")\n", "    print(f\"组件边界: x=({component.xmin:.2f}, {component.xmax:.2f}), y=({component.ymin:.2f}, {component.ymax:.2f})\")\n", "    print(f\"组件尺寸: 宽度={component.xmax - component.xmin:.2f}, 高度={component.ymax - component.ymin:.2f}\")\n", "    \n", "    # 方法2：通过组件的info信息获取（如果有的话）\n", "    if hasattr(component, 'info') and component.info:\n", "        print(f\"组件信息: {component.info}\")\n", "    \n", "    return center_x, center_y\n", "\n", "# 分析现有的coupler\n", "if 'coupler' in locals():\n", "    print(\"=== 环形耦合器分析 ===\")\n", "    center_x, center_y = find_ring_center(coupler)\n", "    \n", "    # 显示coupler的端口信息，这有助于理解结构\n", "    print(\"\\n端口信息:\")\n", "    coupler.pprint_ports()\n", "    \n", "    # 创建一个可视化，标记圆心\n", "    vis_component = gf.Component()\n", "    \n", "    # 添加原始coupler\n", "    coupler_ref = vis_component << coupler\n", "    \n", "    # 在圆心位置添加一个小的标记\n", "    center_mark = gf.components.cross(length=20, width=2, layer=(10, 0))\n", "    center_ref = vis_component << center_mark\n", "    center_ref.move([center_x, center_y])\n", "    \n", "    # 添加一个圆来显示估计的环形区域\n", "    # 假设环的半径大约是组件尺寸的40%\n", "    estimated_radius = min(coupler.xmax - coupler.xmin, coupler.ymax - coupler.ymin) * 0.4\n", "    ring_outline = gf.components.circle(radius=estimated_radius, layer=(11, 0))\n", "    ring_ref = vis_component << ring_outline\n", "    ring_ref.move([center_x, center_y])\n", "    \n", "    print(f\"\\n估计的环半径: {estimated_radius:.2f} μm\")\n", "    print(f\"圆心标记用红色十字表示\")\n", "    print(f\"估计的环轮廓用绿色圆圈表示\")\n", "    \n", "    vis_component.plot()\n", "    vis_component.show()\n", "\n", "# 如果你有组合的组件c，也分析一下\n", "if 'c' in locals():\n", "    print(\"\\n=== 组合组件分析 ===\")\n", "    center_x_c, center_y_c = find_ring_center(c)\n", "    \n", "    # 分析c中的coupler_ref位置\n", "    if hasattr(c, 'references') and len(c.references) > 0:\n", "        for i, ref in enumerate(c.references):\n", "            print(f\"引用 {i}: 位置=({ref.x:.2f}, {ref.y:.2f}), 旋转={ref.rotation}°\")\n", "\n", "# 创建一个函数来帮助用户找到真正的环心\n", "def create_ring_with_center_at(center_x, center_y, radius=100):\n", "    \"\"\"\n", "    在指定位置创建一个环，用于验证圆心位置\n", "    \"\"\"\n", "    test_component = gf.Component()\n", "    \n", "    # 添加原始coupler（如果存在）\n", "    if 'coupler' in locals():\n", "        original_ref = test_component << coupler\n", "    \n", "    # 在指定位置创建一个测试环\n", "    test_ring = gf.components.ring(radius=radius, width=2, layer=(12, 0))\n", "    test_ring_ref = test_component << test_ring\n", "    test_ring_ref.move([center_x, center_y])\n", "    \n", "    # 添加圆心标记\n", "    center_mark = gf.components.cross(length=40, width=3, layer=(13, 0))\n", "    center_ref = test_component << center_mark\n", "    center_ref.move([center_x, center_y])\n", "    \n", "    return test_component\n", "\n", "print(\"\\n=== 使用方法 ===\")\n", "print(\"1. 查看上面的分析结果，获得估计的圆心位置\")\n", "print(\"2. 如果需要调整，可以调用: test = create_ring_with_center_at(x, y, radius)\")\n", "print(\"3. 然后显示: test.show() 来验证圆心位置是否正确\")"]}, {"cell_type": "code", "execution_count": 7, "id": "2d48f9fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 60度圆弧热极 ===\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation       </span>┃<span style=\"font-weight: bold\"> layer         </span>┃<span style=\"font-weight: bold\"> center                                    </span>┃<span style=\"font-weight: bold\"> port_type  </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━┩\n", "│ e1   │ 5.0   │ 60.00000000000001 │ HEATER (47/0) │ (-38.30127018922194, 33.66025403784438)   │ electrical │\n", "│ e2   │ 5.0   │ 300.0             │ HEATER (47/0) │ (-38.30127018922193, -33.660254037844396) │ electrical │\n", "└──────┴───────┴───────────────────┴───────────────┴───────────────────────────────────────────┴────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer        \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type \u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━┩\n", "│ e1   │ 5.0   │ 60.00000000000001 │ HEATER (47/0) │ (-38.30127018922194, 33.66025403784438)   │ electrical │\n", "│ e2   │ 5.0   │ 300.0             │ HEATER (47/0) │ (-38.30127018922193, -33.660254037844396) │ electrical │\n", "└──────┴───────┴───────────────────┴───────────────┴───────────────────────────────────────────┴────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 90度圆弧热极 ===\n", "\n", "=== 120度圆弧热极 ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "\n", "@gf.cell\n", "def heater_arc(\n", "    radius: float = 50,           # 圆弧半径\n", "    angle: float = 60,            # 圆弧角度（度）\n", "    width: float = 2,             # 热极线宽\n", "    pad_length: float = 10,       # 端部pad长度\n", "    pad_width: float = 5,         # 端部pad宽度\n", "    layer: tuple = (47, 0)        # 热极层\n", "):\n", "    \"\"\"\n", "    创建圆弧热极\n", "    \n", "    Args:\n", "        radius: 圆弧半径 (μm)\n", "        angle: 圆弧角度 (度)，y轴为0度\n", "        width: 热极线宽 (μm)\n", "        pad_length: 端部pad长度 (μm) \n", "        pad_width: 端部pad宽度 (μm)\n", "        layer: 层定义\n", "        \n", "    角度定义：y轴为0度，x度的圆弧范围是 180-x/2 到 180+x/2\n", "    \"\"\"\n", "    c = gf.Component()\n", "    \n", "    # 计算圆弧的起始和结束角度（以y轴为0度，向上为正）\n", "    # y轴为0度意味着90度 - angle/2 到 90度 + angle/2\n", "    start_angle = 90 - angle/2  # 度\n", "    end_angle = 90 + angle/2    # 度\n", "    \n", "    # 转换为弧度\n", "    start_rad = np.deg2rad(start_angle)\n", "    end_rad = np.deg2rad(end_angle)\n", "    \n", "    # 1. 创建圆弧路径\n", "    # 生成圆弧上的点\n", "    n_points = max(int(angle * 2), 20)  # 确保足够的点数\n", "    angles = np.linspace(start_rad, end_rad, n_points)\n", "    \n", "    # 计算圆弧上的点坐标\n", "    x_points = radius * np.cos(angles)\n", "    y_points = radius * np.sin(angles)\n", "    points = list(zip(x_points, y_points))\n", "    \n", "    # 创建路径\n", "    path = gf.path.smooth(points, radius=0.1)\n", "    \n", "    # 创建截面\n", "    cross_section = gf.cross_section.cross_section(width=width, layer=layer)\n", "    \n", "    # 创建圆弧热极\n", "    arc_waveguide = path.extrude(cross_section)\n", "    arc_ref = c << arc_waveguide\n", "    \n", "    # 2. 计算端点位置和方向\n", "    # 起始点\n", "    start_x = radius * np.cos(start_rad)\n", "    start_y = radius * np.sin(start_rad)\n", "    start_normal_angle = start_rad + np.pi/2  # 垂直向外的方向\n", "    \n", "    # 结束点\n", "    end_x = radius * np.cos(end_rad)\n", "    end_y = radius * np.sin(end_rad)\n", "    end_normal_angle = end_rad - np.pi/2     # 垂直向外的方向\n", "    \n", "    # 3. 创建起始端的pad，直接连接到圆弧端点\n", "    start_pad = gf.components.rectangle(\n", "        size=(pad_width, pad_length), \n", "        layer=layer\n", "    )\n", "    start_pad_ref = c << start_pad\n", "    \n", "    # 定位起始pad：紧贴圆弧端点，矩形的一端与圆弧相接\n", "    pad_start_x = start_x + (pad_length/2) * np.cos(start_normal_angle)\n", "    pad_start_y = start_y + (pad_length/2) * np.sin(start_normal_angle)\n", "    start_pad_ref.move([pad_start_x, pad_start_y])\n", "    start_pad_ref.rotate(np.degrees(start_normal_angle))\n", "    \n", "    # 4. 创建结束端的pad，直接连接到圆弧端点\n", "    end_pad = gf.components.rectangle(\n", "        size=(pad_width, pad_length), \n", "        layer=layer\n", "    )\n", "    end_pad_ref = c << end_pad\n", "    \n", "    # 定位结束pad：紧贴圆弧端点，矩形的一端与圆弧相接\n", "    pad_end_x = end_x + (pad_length/2) * np.cos(end_normal_angle)\n", "    pad_end_y = end_y + (pad_length/2) * np.sin(end_normal_angle)\n", "    end_pad_ref.move([pad_end_x, pad_end_y])\n", "    end_pad_ref.rotate(np.degrees(end_normal_angle))\n", "    \n", "    # 5. 添加电学端口\n", "    # 起始端口（在pad的外端）\n", "    port1_x = start_x + pad_length * np.cos(start_normal_angle)\n", "    port1_y = start_y + pad_length * np.sin(start_normal_angle)\n", "    c.add_port(\n", "        name=\"e1\",\n", "        center=(port1_x, port1_y),\n", "        width=pad_width,\n", "        orientation=np.degrees(start_normal_angle),\n", "        layer=layer,\n", "        port_type=\"electrical\"\n", "    )\n", "    \n", "    # 结束端口（在pad的外端）\n", "    port2_x = end_x + pad_length * np.cos(end_normal_angle)\n", "    port2_y = end_y + pad_length * np.sin(end_normal_angle)\n", "    c.add_port(\n", "        name=\"e2\",\n", "        center=(port2_x, port2_y),\n", "        width=pad_width,\n", "        orientation=np.degrees(end_normal_angle),\n", "        layer=layer,\n", "        port_type=\"electrical\"\n", "    )\n", "    \n", "    # 扁平化以避免off-grid实例问题\n", "    c.flatten()\n", "    return c\n", "\n", "# 测试不同角度的热极\n", "print(\"=== 60度圆弧热极 ===\")\n", "heater_60 = heater_arc(radius=50, angle=60, width=2)\n", "heater_60.pprint_ports()\n", "heater_60.plot()"]}, {"cell_type": "code", "execution_count": null, "id": "c2f21c24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 创建带热极的环形谐振器 ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173: UserWarning: bend_euler angle should be 90 or 180. Got 140. Use bend_euler_all_angle instead.\n", "  v = func(*args, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-08-17 19:18:41.071\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3954\u001b[0m - \u001b[1mklive v0.3.3: Reloaded file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\3710540427.oas'\u001b[0m\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-08-17 19:30:48.986\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\4168818520.oas'\u001b[0m\n", "\u001b[32m2025-08-17 19:57:26.065\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2687849390.oas'\u001b[0m\n", "\u001b[32m2025-08-17 19:57:26.065\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3948\u001b[0m - \u001b[1mklive v0.3.3: Opened file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2687849390.oas'\u001b[0m\n", "\u001b[32m2025-08-17 19:58:44.532\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3954\u001b[0m - \u001b[1mklive v0.3.3: Reloaded file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2687849390.oas'\u001b[0m\n", "\u001b[32m2025-08-17 19:58:44.532\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mkfactory.kcell\u001b[0m:\u001b[36mshow\u001b[0m:\u001b[36m3954\u001b[0m - \u001b[1mklive v0.3.3: Reloaded file 'd:\\experience\\CV computation\\gds\\gds_github\\gds_cursorv1.0\\build\\gds\\2687849390.oas'\u001b[0m\n"]}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "\n", "\n", "def thermal_heater_ring(\n", "    # Ring parameters\n", "    ring_radius: float = 100,           # Ring radius (μm)\n", "    # Heater parameter\n", "    heater_angle: float = 140,          # Heater arc angle (degrees)\n", "    heater_width: float = 5,            # Heater line width (μm)\n", "    heater_layer: tuple = (49, 0),      # Heater layer\n", "):\n", "    c = gf.Component()\n", "    heater_rad = np.deg2rad(heater_angle)\n", "    # 定义加热器横截面\n", "    cross_section_heater = gf.cross_section.cross_section(\n", "        width=heater_width, layer=heater_layer,\n", "        port_names=(\"e1\", \"e3\"),  # 良好的编程习惯，为电气端口命名\n", "        port_types=(\"electrical\", \"electrical\"),  #\n", "    )\n", "    cross_section_heater_route = gf.cross_section.cross_section(\n", "        width=heater_width, layer=heater_layer,\n", "        port_names=(\"e1\", \"e3\"),  # 良好的编程习惯，为电气端口命名\n", "        port_types=(\"electrical\", \"electrical\")  #\n", "    )\n", "   # cross_check=gf.get_cross_section(\"metal_routing\", width=heater_width, layer=heater_layer)\n", "    # 使用gdsfactory的 arc 函数创建热极圆弧\n", "    heater_waveguide =gf.components.bend_circular(radius=ring_radius, angle=heater_angle, \n", "                                                       cross_section=cross_section_heater, \n", "                                                       allow_min_radius_violation=False)\n", "    heater_ref = c << heater_waveguide\n", "    #x=heater_ref.xmin()\n", "    c.rotate(-heater_angle/2)\n", "    points_left=[(heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)),\n", "            (heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)+10),\n", "            (-heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)+10),\n", "           (-heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2))]\n", "    c.add_polygon(points_left, layer=heater_layer)\n", "    points_right=[(2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)),\n", "            (2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)+10),\n", "           (2*ring_radius*np.sin(heater_rad/2)-heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)+10),\n", "           (2*ring_radius*np.sin(heater_rad/2)-heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2))]\n", "    c.add_polygon(points_right, layer=heater_layer)\n", "    c2=gf.Component()\n", "    c2.add_port(\n", "        name=\"e1\",\n", "        center=(-heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)+5),\n", "        width=5,\n", "        orientation=180,\n", "        layer=heater_layer,\n", "        port_type=\"electrical\"\n", "    )\n", "    c2.add_port(\n", "        name=\"e2\",\n", "        center=(2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)+5),\n", "        width=5,\n", "        orientation=0,\n", "        layer=heater_layer,\n", "        port_type=\"electrical\"\n", "    )\n", "    rinner = 2000  # \tThe circle radius of inner corners (in database units).\n", "    router = 2000  # \tThe circle radius of outer corners (in database units).\n", "    n=2000\n", "    c.flatten()\n", "    for layer, polygons in c.get_polygons().items():\n", "        for p in polygons:\n", "            p_round = p.round_corners(rinner, router, n)\n", "            c2.add_polygon(p_round, layer=layer)\n", "    pad_ref1=c2<<gf.components.pad(size=(10, 10), layer=heater_layer, port_inclusion=0, port_orientation=0, port_orientations=(180, 90, 0, -90), port_type='pad')\n", "    pad_ref1.center=(-heater_width/2*np.sin(heater_rad/2)-20,heater_width/2*np.cos(heater_rad/2)+5)\n", "    \n", "    pad_ref2=c2<<gf.components.pad(size=(10, 10), layer=heater_layer, port_inclusion=0, port_orientation=0, port_orientations=(180, 90, 0, -90), port_type='pad')\n", "    pad_ref2.center=(2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2)+20,-heater_width/2*np.cos(heater_rad/2)+5)\n", "    \n", "    \n", "    route1 = gf.routing.route_single(\n", "        c2,\n", "        pad_ref1.ports[\"e3\"],\n", "        c2.ports[\"e1\"],\n", "        bend=\"wire_corner45\",\n", "        port_type=\"electrical\",\n", "        cross_section=cross_section_heater_route,\n", "        allow_width_mismatch=True,\n", "    )\n", "    route2 = gf.routing.route_single(\n", "        c2,\n", "        pad_ref2.ports[\"e1\"],\n", "        c2.ports[\"e2\"],\n", "        bend=\"wire_corner45\",\n", "        port_type=\"electrical\",\n", "        cross_section=cross_section_heater_route,\n", "        allow_width_mismatch=True,\n", "        auto_taper=True,\n", "    )\n", "    c3=gf.Component()\n", "    c3<<c2\n", "    c3.add_port(\"e1\",port=pad_ref1.ports[\"e2\"])\n", "    c3.add_port(\"e2\",port=pad_ref2.ports[\"e2\"])\n", "    c3.add_port(\"e3\",port=pad_ref1.ports[\"e4\"])\n", "    c3.add_port(\"e4\",port=pad_ref2.ports[\"e4\"])\n", "    #c3.draw_ports()\n", "    c3.flatten()\n", "    return c3\n", "\n", "# 创建和测试带热极的环形器件\n", "print(\"=== 创建带热极的环形谐振器 ===\")\n", "ring_heater = thermal_heater_ring()\n", "ring_heater.draw_ports()\n", "ring_heater.plot()\n", "ring_heater.show()\n"]}, {"cell_type": "code", "execution_count": 5, "id": "6d68031c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "c1=gf.Component()\n", "c = gf.components.bend_circular(angle=90, cross_section='strip', allow_min_radius_violation=False).copy()\n", "c1<<c\n", "c1.draw_ports()\n", "c1.plot()"]}, {"cell_type": "code", "execution_count": 22, "id": "fcbde2f4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "\n", "c = gf.components.pad(size=(100, 100), layer=(1,0), port_inclusion=0, port_orientation=0, port_orientations=(180, 90, 0, -90), port_type='pad').copy()\n", "c.draw_ports()\n", "c.plot()\n", "c.show()"]}, {"cell_type": "code", "execution_count": null, "id": "d10abb91", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "4b3760c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 周期性电极示例 ===\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 1.5   │ 180.0       │ WG (1/0) │ (-260.0, 0.0) │ optical   │\n", "│ o2   │ 1.5   │ 0.0         │ WG (1/0) │ (260.0, 0.0)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 1.5   │ 180.0       │ WG (1/0) │ (-260.0, 0.0) │ optical   │\n", "│ o2   │ 1.5   │ 0.0         │ WG (1/0) │ (260.0, 0.0)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "@gf.cell\n", "def periodic_electrode(\n", "    width: float = 500,              # 电极总宽度 (μm)\n", "    height: float = 2,        # 上矩形高度 (μm)\n", "    gap: float = 10,                # 上下矩形间距 (μm)\n", "    period: float = 5,              # 连接条周期 (μm)\n", "    duty_cycle: float = 0.2,        # 占空比 (0-1)\n", "    layer: tuple = (4, 0)           # 金属层\n", "):\n", "    \"\"\"\n", "    创建周期性电极结构\n", "    \n", "    Args:\n", "        width: 电极总宽度 (μm)\n", "        height_upper: 上矩形高度 (μm)\n", "        height_lower: 下矩形高度 (μm)\n", "        gap: 上下矩形间距 (μm)\n", "        period: 连接条周期 (μm)\n", "        duty_cycle: 占空比，连接条占周期的比例 (0-1)\n", "        connector_width: 连接条宽度 (μm)\n", "        layer: 金属层定义\n", "    \n", "    Returns:\n", "        gf.Component: 周期性电极组件\n", "    \"\"\"\n", "    c = gf.Component()\n", "    \n", "    # 1. 创建上矩形\n", "    upper_rect = gf.components.rectangle(\n", "        size=(width, height), \n", "        layer=layer\n", "    )\n", "    upper_ref = c << upper_rect\n", "    upper_ref.move([0, gap/2 ])\n", "    \n", "    # 2. 创建下矩形\n", "    lower_rect = gf.components.rectangle(\n", "        size=(width, height), \n", "        layer=layer\n", "    )\n", "    lower_ref = c << lower_rect\n", "    lower_ref.move([0, -gap/2 - height])\n", "    \n", "    # 3. 创建周期性连接条\n", "    connector_length = duty_cycle * period  # 连接条长度\n", "    spacing = period - connector_length     # 连接条间距\n", "    \n", "    # 计算可以放置的连接条数量\n", "    num_connectors = int(width / period)\n", "    x_space=(width-num_connectors*period+spacing)/2\n", "    # 创建每个连接条\n", "    for i in range(num_connectors):\n", "        # 计算连接条的x位置\n", "        x_start =  i * period + x_space\n", "        x_center = x_start + connector_length/2\n", "        connector = gf.components.rectangle(\n", "            size=(connector_length, gap+height/2), \n", "            layer=layer\n", "        )\n", "        connector_ref = c << connector\n", "        #connector_ref.dmove([x_center, 0])\n", "        connector_ref.center=(x_center,0)\n", "    c.center=(0,0)\n", "    radius=50\n", "    x_pad=20\n", "    distance=20\n", "    y_pad=gap/2+height+distance+radius\n", "    \n", "    # 创建圆形pad\n", "    pad1 = gf.components.circle(radius=radius, layer=layer)\n", "    pad_ref1 = c << pad1\n", "    pad_ref1.center=(0, y_pad)\n", "    points=[(2,gap/2+height),(2,gap/2+height+distance+0.3*radius),(-2,gap/2+height+distance+0.3*radius),(-2,gap/2+height)]\n", "    c.add_polygon(points, layer=layer)\n", "    c.flatten()\n", "    c2=gf.Component()\n", "    rinner = 50  # \tThe circle radius of inner corners (in database units).\n", "    router = 50  # \tThe circle radius of outer corners (in database units).\n", "    n=2000\n", "    for layer, polygons in c.get_polygons().items():\n", "        for p in polygons:\n", "            p_round = p.round_corners(rinner, router, n)\n", "            c2.add_polygon(p_round, layer=layer)\n", "    return c2\n", "@gf.cell\n", "def waveguide_with_electrode(length: float = 500, \n", "                            height: float = 2,    \n", "                            wg_width:float=1.5,   \n", "                            gap: float = 10,              \n", "                            period: float = 5,              \n", "                            duty_cycle: float = 0.2,       \n", "                            layer_electrode: tuple = (4, 0), ):\n", "    c = gf.Component()\n", "    electrode_ref = c << periodic_electrode(width=length,           \n", "                                    height=height,    \n", "                                    gap=gap,                \n", "                                    period=period,             \n", "                                    duty_cycle=duty_cycle,        \n", "                                    layer=layer_electrode,)\n", "    wg_section = gf.cross_section.cross_section(\n", "        width=wg_width,\n", "        offset=0,\n", "        layer=(1, 0)\n", "    )\n", "    wg_ref=c<<gf.components.straight(length=length+20, npoints=2, cross_section=wg_section)\n", "    wg_ref.center=(0,0)\n", "    c.add_ports(wg_ref.ports)\n", "    return c\n", "    \n", "# 创建几个不同参数的电极示例\n", "print(\"=== 周期性电极示例 ===\")\n", "\n", "# 示例1：基本参数\n", "electrode1 = waveguide_with_electrode()\n", "electrode1.pprint_ports()\n", "electrode1.plot()\n", "electrode1.draw_ports()\n", "# 显示第一个示例\n", "electrode1.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f31580a1", "metadata": {}, "outputs": [], "source": ["# =============================================================================\n", "# 用户自定义参数示例\n", "# =============================================================================\n", "\n", "def create_custom_electrode():\n", "    \"\"\"\n", "    用户可以在这里修改参数来创建自定义电极\n", "    \"\"\"\n", "    # 用户可以修改这些参数\n", "    user_params = {\n", "        'width': 100,           # 电极总宽度 (μm)\n", "        'height_upper': 3,      # 上矩形高度 (μm)  \n", "        'height_lower': 3,      # 下矩形高度 (μm)\n", "        'gap': 15,              # 上下矩形间距 (μm)\n", "        'period': 8,            # 连接条周期 (μm)\n", "        'duty_cycle': 0.6,      # 占空比 (0-1)\n", "        'connector_width': 1.5, # 连接条宽度 (μm)\n", "        'layer': (4, 0)         # 金属层\n", "    }\n", "    \n", "    print(\"=== 用户自定义电极参数 ===\")\n", "    print(f\"总宽度: {user_params['width']}μm\")\n", "    print(f\"上矩形高度: {user_params['height_upper']}μm\")\n", "    print(f\"下矩形高度: {user_params['height_lower']}μm\")\n", "    print(f\"间距: {user_params['gap']}μm\")\n", "    print(f\"周期: {user_params['period']}μm\")\n", "    print(f\"占空比: {user_params['duty_cycle']}\")\n", "    print(f\"连接条宽度: {user_params['connector_width']}μm\")\n", "    \n", "    # 计算一些有用的信息\n", "    connector_length = user_params['duty_cycle'] * user_params['period']\n", "    num_connectors = int(user_params['width'] / user_params['period'])\n", "    \n", "    print(f\"\\n=== 计算结果 ===\")\n", "    print(f\"连接条长度: {connector_length:.2f}μm\")\n", "    print(f\"连接条数量: {num_connectors}\")\n", "    print(f\"连接条间隔: {user_params['period'] - connector_length:.2f}μm\")\n", "    \n", "    # 创建电极\n", "    electrode = periodic_electrode(**user_params)\n", "    \n", "    return electrode\n", "\n", "# 创建并显示自定义电极\n", "custom_electrode = create_custom_electrode()\n", "custom_electrode.plot()\n", "custom_electrode.show()\n", "\n", "print(\"\\n=== 端口信息 ===\")\n", "custom_electrode.pprint_ports()"]}, {"cell_type": "code", "execution_count": null, "id": "ef8d0be1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}