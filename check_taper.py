"""检查默认PDK中的taper组件"""

import gdsfactory as gf

print("=== 检查默认PDK中的taper组件 ===")

# 获取默认PDK
default_pdk = gf.get_active_pdk()
print(f"默认PDK: {default_pdk.name}")

# 检查cells中的taper相关组件
print("\n默认PDK中的taper相关组件:")
taper_components = []
for name, component in default_pdk.cells.items():
    if 'taper' in name.lower():
        taper_components.append(name)
        print(f"  - {name}")

if not taper_components:
    print("  没有找到taper相关组件")

# 检查是否有标准的taper组件
print("\n检查标准taper组件:")
try:
    taper = gf.components.taper()
    print(f"  标准taper组件存在: {taper.name}")
    print(f"  端口: {list(taper.ports.keys())}")
    for port_name, port in taper.ports.items():
        print(f"    {port_name}: width={port.width}, layer={port.layer}")
except Exception as e:
    print(f"  标准taper组件不存在: {e}")

# 检查electrical taper
print("\n检查electrical taper:")
try:
    # 尝试创建一个electrical taper
    electrical_taper = gf.components.taper(
        width1=5.0,
        width2=10.0,
        length=10.0,
        layer=(49, 0),  # HEATER layer
        port_type="electrical"
    )
    print(f"  electrical taper创建成功: {electrical_taper.name}")
    print(f"  端口: {list(electrical_taper.ports.keys())}")
    for port_name, port in electrical_taper.ports.items():
        print(f"    {port_name}: width={port.width}, layer={port.layer}, type={port.port_type}")
except Exception as e:
    print(f"  electrical taper创建失败: {e}")

print("\n=== 检查完成 ===")
